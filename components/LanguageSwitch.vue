<template>
  <div class="relative group">
    <!-- Language Switcher Button -->
    <div class="flex items-center cursor-pointer">
      <span>{{ $i18n.locale }}</span>
      <!-- Dropdown Arrow Icon -->
      <svg class="w-3 mt-0.5 ml-1 rotate-90 fill-body-black dark:fill-body-gray"
           xmlns="http://www.w3.org/2000/svg"
           width="16" height="16"
           viewBox="0 -960 960 960">
        <path d="m321-80-71-71 329-329-329-329 71-71 400 400L321-80Z"/>
      </svg>
    </div>

    <!-- Dropdown Menu -->
    <ul
      class="min-w-32 absolute top-10 -left-10 hidden group-hover:block pt-2 pb-4 px-4 bg-onvocado-white text-black border-2 border-body-black rounded-xl"
      :class="$style.submenu">
      <!-- Menu Header -->
      <li class="py-1 mb-2 border-b-2 border-onvocado-primary-dark">
        <span class="text-body-black">{{ $t('Language') }}</span>
      </li>
      <!-- Language Options -->
      <li v-for="locale in availableLocales" :key="locale.code" class="py-1 flex">
        <NuxtLink
          :to="switchLocalePathWithTrailingSlash(locale.code)"
          :aria-label="locale.code"
          :data-hover="locale.code"
          class="!text-body-black transition-colors duration-300">
          {{ locale.code }}
        </NuxtLink>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  computed: {
    availableLocales() {
      return this.$i18n.locales.filter(locale => locale.code !== this.$i18n.locale)
    }
  }
}
</script>

<style lang="scss" module>
.submenu {
  &::before {
    content: "";
    height: 20px;
    position: absolute;
    width: 100%;
    margin-top: -28px;
    margin-left: 0;
    margin-right: 0;
    left: 0;
    right: 0;
  }
}
</style>
