<template>
  <div class="w-full m-auto px-5 py-14 md:pt-32 md:mb-28 flex-col">
    <div :class="$style.worksEverywhere" class="w-full m-auto mb-24 md:mb-36">
      <div :class="$style.details">
        <h2 class="text-4xl font-extrabold mb-4">{{ $t('supportSection.zoneTitle') }}</h2>
        <div v-html="$t('supportSection.zoneDetails')" class="markdown mb-8"></div>
        <a href="https://app.onvocado.com/register" class="button big">{{ $t('supportSection.tryFree') }}</a>
      </div>

      <div :class="$style.bundle">
        <div v-for="(logo, index) in logos" :key="index">
          <nuxt-img :src="logo.src" :alt="$t(logo.altKey)" width="100" height="50" lazy/>
        </div>
      </div>
    </div>

    <div class="w-full m-auto xl:max-w-screen-xl container flex flex-col lg:flex-row-reverse">
      <div class="max-w-xl mb-12">
        <h2 class="mb-4 md:ml-16 lg:ml-24 text-4xl font-extrabold">{{ $t('supportSection.integratesTitle') }}</h2>
        <p class="md:ml-16 lg:ml-24 text-pretty leading-6">{{ $t('supportSection.integratesDescription') }}</p>
      </div>
      <div class="lg:w-1/2 flex flex-col justify-center items-center gap-8 xl:gap-10">
        <!-- First row of integrations animated left -->
        <div
          class="w-full inline-flex flex-nowrap overflow-hidden [mask-image:_linear-gradient(to_right,transparent_0,_black_128px,_black_calc(100%-128px),transparent_100%)]">
          <ul
            ref="integrationLogosRow1"
            class="flex items-center justify-center md:justify-start animate-scroll-left [&_li]:mx-4 [&_li]:md:mx-8 [&_img]:max-w-28 [&_img]:opacity-60 gap-x-12">
            <li v-for="(logo, index) in firstRowLogos" :key="index">
              <img :src="logo.src" :alt="$t(logo.altKey)" :width="logo.width" :height="logo.height"/>
            </li>
          </ul>
        </div>

        <!-- Second row of integrations animated right -->
        <div
          class="w-full inline-flex flex-nowrap overflow-hidden [mask-image:_linear-gradient(to_right,transparent_0,_black_128px,_black_calc(100%-128px),transparent_100%)]">
          <ul
            ref="integrationLogosRow2"
            class="flex items-center justify-center md:justify-start animate-scroll-right [&_li]:mx-4 [&_li]:md:mx-8 [&_img]:max-w-28 [&_img]:opacity-60 gap-x-12">
            <li v-for="(logo, index) in secondRowLogos" :key="index">
              <img :src="logo.src" :alt="$t(logo.altKey)" :width="logo.width" :height="logo.height"/>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "SupportSection",
  props: ['zone'],
  data() {
    return {
      logos: [
        {src: '/images/logos/wordpress_monotone_black.svg', altKey: 'supportSection.logoWordPress'},
        {src: '/images/logos/magento_monotone_black.svg', altKey: 'supportSection.logoMagento'},
        {src: '/images/logos/squarespace_monotone_black.svg', altKey: 'supportSection.logoSquarespace'},
        {src: '/images/logos/woocomerce_monotone_black.svg', altKey: 'supportSection.logoWooCommerce'},
        {src: '/images/logos/shopify_monotone_black.svg', altKey: 'supportSection.logoShopify'}
      ],
      integrationLogos: [
        { src: '/images/logos/integrations/activecampaign.svg', altKey: 'supportSection.integrationActiveCampaign', width: 516, height: 53 },
        { src: '/images/logos/integrations/mailchimp.svg', altKey: 'supportSection.integrationMailchimp', width: 185, height: 60 },
        { src: '/images/logos/integrations/webhook.svg', altKey: 'supportSection.integrationWebhook', width: 159, height: 44 },
        { src: '/images/logos/integrations/zapier.svg', altKey: 'supportSection.integrationZapier', width: 80, height: 40 },
        { src: '/images/logos/integrations/klaviyo.svg', altKey: 'supportSection.integrationKlaviyo', width: 160, height: 40 },
        { src: '/images/logos/integrations/sendfox.svg', altKey: 'supportSection.integrationSendfox', width: 150, height: 40 },
        { src: '/images/logos/integrations/brevo.svg', altKey: 'supportSection.integrationBrevo', width: 140, height: 40 },
        { src: '/images/logos/integrations/twilio-sendgrid.svg', altKey: 'supportSection.integrationSendgrid', width: 180, height: 50 },
        { src: '/images/logos/integrations/getresponse.svg', altKey: 'supportSection.integrationGetResponse', width: 200, height: 50 },
        { src: '/images/logos/integrations/aweber.svg', altKey: 'supportSection.integrationAweber', width: 170, height: 40 },
        { src: '/images/logos/integrations/omnisend.svg', altKey: 'supportSection.integrationOmnisend', width: 170, height: 40 },
        { src: '/images/logos/integrations/mailjet.svg', altKey: '', width: 170, height: 40 },
      ],
    };
  },
  computed: {
    firstRowLogos() {
      const halfLength = Math.ceil(this.integrationLogos.length / 2);
      return [...this.integrationLogos.slice(0, halfLength), ...this.integrationLogos.slice(0, halfLength)];
    },
    secondRowLogos() {
      const halfLength = Math.ceil(this.integrationLogos.length / 2);
      return [...this.integrationLogos.slice(halfLength), ...this.integrationLogos.slice(halfLength)];
    },
  },
};
</script>

<style lang="scss">
@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes scroll-right {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}

.animate-scroll-left {
  animation: scroll-left 25s linear infinite;
}

.animate-scroll-right {
  animation: scroll-right 25s linear infinite;
}
</style>

<style lang="scss" module>
.worksEverywhere {
  @apply xl:max-w-screen-xl container;
  position: relative;
  text-align: center;
  overflow: visible;
  display: flex;
  flex-direction: column;

  @media screen and (min-width: 1140px) {
    text-align: left;
    flex-direction: row;
  }
}

.details {
  margin: 0 auto 80px auto;
  text-align: left;

  @media screen and (min-width: 768px) {
    margin: 0;
  }
}

$opacity: 0.59;
$shadow-color: rgba(#e7edd1, 0.75);
$background-color: rgba(#e7edd1, $opacity);

.bundle {
  width: 100%;
  height: 80vw;
  margin: auto;
  position: relative;

  @media screen and (min-width: 1140px) {
    width: 50%;
    height: 37vw;
  }

  @media screen and (min-width: 1900px) {
    div {
      width: 12vw;
      height: 12vw;
    }
  }

  img {
    width: 65%;
    height: 40%;
  }

  div {
    width: 32vw;
    height: 32vw;
    position: absolute;
    border-radius: 50%;
    box-shadow: 1px 8px 20px 0 $shadow-color;
    display: flex;
    align-items: center;
    justify-content: center;

    @media screen and (min-width: 1140px) {
      width: 14vw;
      height: 14vw;
    }

    &:nth-of-type(1) {
      top: 20vw;
      right: 4vw;
      transform: scale(0.85);
      background-color: $background-color;

      @media screen and (min-width: 1140px) {
        top: 18vw;
        left: 27vw;
        right: auto;
      }
    }

    &:nth-of-type(2) {
      top: 21vw;
      left: 26vw;
      transform: scale(0.40);

      @media screen and (min-width: 1140px) {
        top: 11vw;
        left: 16vw;
      }
    }

    &:nth-of-type(3) {
      top: 40vw;
      left: 0;
      transform: scale(0.97);
      background-color: $background-color;

      @media screen and (min-width: 1140px) {
        top: 10vw;
        left: -3vw;
        transform: scale(0.55);
      }
    }

    &:nth-of-type(4) {
      top: 51vw;
      left: 37vw;
      transform: scale(0.67);

      @media screen and (min-width: 1140px) {
        top: 19vw;
        left: 8vw;
      }
    }

    &:nth-of-type(5) {
      top: 0;
      left: 12vw;
      transform: scale(0.77);
      background-color: $background-color;

      @media screen and (min-width: 1140px) {
        top: 0;
        left: 12vw;
      }
    }

    &:nth-of-type(6) {
      top: -30px;
      left: 44vw;
      transform: scale(0.61);

      @media screen and (min-width: 1140px) {
        top: -12px;
        left: 28vw;
      }
    }
  }
}
</style>
