<template>
  <div class="container max-w-screen-xl py-16 mx-auto">
    <h1 class="mb-8">{{ zone.title }}</h1>
    <div v-html="zone?.details" class="markdown mb-4"></div>
    <ResponsivePicture v-if="imageAttributes !== null"
                       class="w-full object-contain m-0"
                       :imgClass="'w-full'"
                       :isImgFullscreen="true"
                       :alt="alternativeText"
                       :caption="caption"
                       :smallURL="getImageOfSize(imageAttributes, 'small')"
                       :mediumURL="getImageOfSize(imageAttributes, 'medium')"
                       :largeURL="getImageOfSize(imageAttributes, 'large')"
                       :xlargeURL="getImageOfSize(imageAttributes, 'xlarge')"/>
  </div>
</template>

<script>
import ImageMeta from "@/mixins/imageMeta";

export default {
  name: "ImageSection",
  props: ['zone'],
  mixins: [ImageMeta],
  computed: {
    imageAttributes() {
      return this.getAttribute(this.zone, 'image.data.attributes');
    }
  },
}
</script>
