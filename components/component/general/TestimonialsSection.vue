<template>
  <div class="container max-w-screen-xl py-8 md:py-16 mx-auto">
    <h2 class="mb-12 md:mb-16 text-center text-5xl md:text-6xl font-extrabold">{{ zone.title }}</h2>

    <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
      <!-- Loop over each row of testimonials -->
      <div v-for="(row, rowIndex) in groupedTestimonials"
           :key="rowIndex"
           class="grid gap-4"
           :class="{ 'hidden lg:grid': rowIndex >= 3 }">
        <!-- Loop over each testimonial in the row -->
        <div v-for="(testimonial, testimonialIndex) in row" :key="testimonial.name"
             class="md:flex rounded-3xl bg-body-gray-dark"
             :class="{ 'hidden md:block': rowIndex * row.length + testimonialIndex >= 3 }">
          <div class="p-8 w-full">
            <div class="flex items-center">
              <img class="h-10 w-10 rounded-full mr-4"
                   v-if="testimonial.image.data?.attributes?.formats?.thumbnail"
                   width="40px"
                   height="40px"
                   :src="testimonial.image.data.attributes.formats.thumbnail.url"
                   :alt="testimonial.image.data.attributes.formats.thumbnail.alternativeText">
              <div class="text-sm flex flex-col">
                <span class="text-body-black leading-none">{{ testimonial.name }}</span>
                <span class="text-body-black font-bold">{{ testimonial.role }}</span>
              </div>
            </div>
            <div class="markdown mt-4 leading-loose text-body-black" v-html="testimonial.text">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "TestimonialsSection",
  props: ['zone'],
  computed: {
    groupedTestimonials() {
      // Logic to group testimonials into rows
      // This is a simple example and can be adjusted based on your specific requirements
      const rows = [];
      const itemsPerRow = 3; // Adjust as needed

      for (let i = 0; i < this.zone.testimonials.length; i += itemsPerRow) {
        rows.push(this.zone.testimonials.slice(i, i + itemsPerRow));
      }

      return rows;
    },
  },
}
</script>
