<template>
  <div class="container md:py-16 mx-auto">
    <div class="mb-16 text-center" v-if="zone.title || zone.subtitle">
      <h3 class="text-base font-semibold tracking-wide text-body-black uppercase" v-if="zone.subtitle">
        {{ zone.subtitle }}
      </h3>
      <h2 class="mt-2 text-3xl font-extrabold leading-8 tracking-tight text-gray-900 dark:text-white sm:text-4xl" v-if="zone.title">
        {{ zone.title }}
      </h2>
    </div>
    <div class="flex flex-wrap my-8 md:my-12 dark:text-white md:divide-x-2 divide-y-2">
      <div
        v-for="(feature, index) in zone.features"
        :key="index"
        class="w-full p-8 md:w-1/2 lg:w-1/3
        lg:[&:nth-child(3)]:!border-t-0
        md:[&:nth-child(2)]:!border-t-0
        lg:[&:nth-child(4)]:!border-l-0"
        :class="{
          '!border-l-0 lg:!border-l-2': index % 2 === 0 && index !== 0,
          'lg:!border-l-0': index === 0,
        }">
        <div class="flex items-center mb-6">
          <img v-if="feature.icon?.data?.attributes?.url" class="w-6 h-6" :src="feature.icon?.data?.attributes?.url" :alt="feature.icon?.data?.attributes?.alternativeText">
          <div class="text-xl font-semibold" :class="feature.icon?.data?.attributes?.url ? 'ml-4' : ''">
            {{ feature.title }}
          </div>
        </div>
        <div v-html="feature.details" class="markdown leading-loose text-md"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "FeaturesGridSection",
  props: ['zone'],
}
</script>
