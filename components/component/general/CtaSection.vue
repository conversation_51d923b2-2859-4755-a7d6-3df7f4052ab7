<template>
  <div class="bg-body-black">
    <div class="xl:max-w-5xl py-16 container">
      <div class="text-center">
        <h2 class="mb-8 md:mb-4 text-5xl md:text-6xl font-extrabold text-onvocado-primary">
          <span v-if="zone.title" class="block">{{ $t('ctaSection.zoneTitle') }}</span>
          <span v-if="zone.subtitle" class="block text-onvocado-primary">{{ $t('ctaSection.zoneSubtitle') }}</span>
        </h2>
        <div v-if="zone.details" v-html="$t('ctaSection.zoneDetails')"
             class="markdown text-xl mt-4 max-w-md mx-auto text-body-gray"></div>
        <div v-if="zone.button?.link" class="lg:mt-0 lg:flex-shrink-0">
          <div class="mt-12 inline-flex rounded-md shadow">
            <a :href="zone.button.link" class="button dark-bg">{{ $t('ctaSection.buttonText') }} - <span
              class="font-normal opacity-80">{{ $t('ctaSection.freeText') }}</span></a>
          </div>
        </div>
      </div>

      <div class="mt-12 md:mt-16">
        <p v-if="zone.sideText" class="text-body-gray text-left text-lg font-semibold">{{ $t('ctaSection.whoUses') }}</p>

        <Cards v-if="zone.cards" :cards="zone.cards" :dark-bg="true"/>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  name: "CtaSection",
  props: ['zone'],
}
</script>z
