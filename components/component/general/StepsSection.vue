<template>
  <div class="py-16 bg-body-black text-body-gray">
    <div class="xl:max-w-5xl container">
      <h2 class="mb-6 text-4xl">{{ zone.title }}</h2>
      <ul class="mb-10 flex flex-col gap-8">

        <li v-for="(step, index) in zone.steps" :key="index" class="flex items-center gap-4">
          <div
            class="h-8 md:h-12 w-8 md:w-12 aspect-square rounded-full border-2 border-onvocado-primary flex items-center justify-center text-xl font-bold">
            {{ index + 1 }}
          </div>
          <div>
            <h3>{{ step.title }}</h3>
            <div v-html="step.details" class="markdown leading-loose"></div>
          </div>
        </li>
      </ul>
      <div v-if="zone.button?.link" class="inline-block">
        <a :href="zone.button.link" class="button dark-bg medium text-[17px] block">{{ zone.button.text }}</a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "StepsSection",
  props: ['zone'],
}
</script>
