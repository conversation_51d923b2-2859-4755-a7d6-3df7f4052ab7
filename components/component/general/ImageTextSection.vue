<!-- ImageTextSection.vue -->
<template>
  <div class="container max-w-screen-xl py-10 lg:py-12 mx-auto">
    <div class="w-full flex items-center justify-between flex-col sm:flex-row-reverse gap-x-8 md:gap-x-16 xl:gap-x-24 2xl:gap-x-32"
         :class="{ 'md:!flex-row': zone.imageFirst }">
      <div class="w-full flex-1">
        <div class="w-full relative mb-4 md:mb-0">

          <template v-if="imageAttributes?.provider_metadata?.resource_type === 'video'">
            <component ref="videoPlayerComponent" :is="VideoPlayer" :options="videoOptions"/>
          </template>
          <template v-else>
            <ResponsivePicture v-if="imageAttributes !== null"
                               :imgClass="'h-full m-auto'"
                               :isImgFullscreen="true"
                               :alt="alternativeText"
                               :caption="caption"
                               :smallURL="getImageOfSize(imageAttributes, 'small')"
                               :mediumURL="getImageOfSize(imageAttributes, 'medium')"
                               :largeURL="getImageOfSize(imageAttributes, 'large')"
                               :xlargeURL="getImageOfSize(imageAttributes, 'xlarge')"/>
          </template>
        </div>
      </div>
      <div class="flex-1 text-left">
        <div class="mb-1 md:mb-3">
          <span class="text-sm font-bold uppercase text-onvocado-primary-dark">{{ zone.subtitle }}</span>
        </div>
        <div>
          <h2 class="mb-4 md:mb-6 text-3xl md:text-5xl font-inter font-normal">{{ zone.title }}</h2>
        </div>
        <div v-html="zone?.details" class="markdown">
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ImageMeta from "@/mixins/imageMeta.js";

export default {
  name: "ImageTextSection",
  mixins: [ImageMeta],
  props: ['zone'],
  data() {
    return {
      VideoPlayer: null,
      observer: null
    };
  },
  computed: {
    isVideo() {
      return this.imageAttributes && this.imageAttributes.provider_metadata?.resource_type === 'video';
    },
    imageAttributes() {
      return this.getAttribute(this.zone, 'image.data.attributes');
    },
    videoOptions() {
      if (this.zone.videoOnce) {
        return {
          autoplay: false,
          controls: false,
          loop: false,
          muted: true,
          sources: [
            {
              src: this.imageAttributes ? this.imageAttributes.url : '',
              type: this.imageAttributes ? this.imageAttributes.mime : '',
            }
          ]
        }
      } else {
        return {
          autoplay: true,
          controls: false,
          loop: true,
          muted: true,
          sources: [
            {
              src: this.imageAttributes ? this.imageAttributes.url : '',
              type: this.imageAttributes ? this.imageAttributes.mime : '',
            }
          ]
        }
      }
    }
  },
  created() {
    if (this.imageAttributes && this.imageAttributes.provider_metadata?.resource_type === 'video') {
      import('@/components/VideoPlayer.vue').then(module => {
        this.VideoPlayer = module.default;
      });
    }
  },
  mounted() {
    if (this.zone.videoOnce && this.isVideo) {
      this.setupObserver();
    }
  },
  updated() {
    if (this.zone.videoOnce && this.isVideo) {
      this.setupObserver();
    }
  },
  methods: {
    setupObserver() {
      this.$nextTick(() => {
        if (this.$refs.videoPlayerComponent && !this.observer) {
          const options = {
            root: null,
            rootMargin: '0px',
            threshold: 0.5
          };
          this.observer = new IntersectionObserver(this.handleIntersection, options);
          this.observer.observe(this.$refs.videoPlayerComponent.$el);
        }
      });
    },
    handleIntersection(entries) {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          if (this.$refs.videoPlayerComponent && this.$refs.videoPlayerComponent.play) {
            this.$refs.videoPlayerComponent.play();
          }
          if (this.observer) {
            this.observer.unobserve(entry.target);
          }
        }
      });
    }
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}
</script>
