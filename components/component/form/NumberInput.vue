<template>
  <label class="block">
    <span class="text-gray-700">{{ zone.label }} {{ zone.required ? '*' : '' }}</span>
    <input
      :value="value"
      @input="$emit('input', $event.target.value)"
      type="number"
      :class="['w-full px-3 py-2.5 mt-1 text-sm appearance-none block rounded-lg bg-transparent border border-onvocado-gray-light shadow-sm focus:border-onvocado-primary focus:ring focus:ring-onvocado-primary focus:ring-opacity-50 outline outline-0 transition-all focus:outline-0',
        {'!border-onvocado-secondary': error}]"
      placeholder=""
    />
  </label>
</template>

<script>
export default {
  name: "FormNumber",
  props: {
    zone: Object,
    value: String,
    error: Boolean
  },
}
</script>
