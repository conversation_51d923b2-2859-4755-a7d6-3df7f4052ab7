<template>
  <label class="block">
    <span class="text-gray-700">{{ zone.label }} {{ zone.required ? '*' : '' }}</span>

    <div class="h-11 relative">
      <select
        :class="['w-full px-3 py-2.5 mt-1 appearance-none text-sm block rounded-lg bg-transparent border border-onvocado-gray-light shadow-sm focus:border-onvocado-primary focus:ring focus:ring-onvocado-primary focus:ring-opacity-50 outline outline-0 transition-all focus:outline-0',
        {'!border-onvocado-secondary': error}, $style.select]"
        @input="$emit('input', $event.target.value)">
        <option v-for="option in zone.options" :key="option.id" :value="option.value">{{ option.text }}</option>
      </select>
    </div>
  </label>
</template>

<script>
export default {
  name: "FormSelect",
  props: {
    zone: Object,
    value: String,
    error: Boolean
  },
}
</script>

<style lang="scss" module>
.select {
    background-image: url('/icons/arrow.svg');
    background-position: right 0.25rem center;
    background-repeat: no-repeat;
    background-size: 1.50em 1.50em;
}
</style>
