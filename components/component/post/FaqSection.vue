<template>
  <div class="section-wrapper text xl:max-w-4xl container" itemscope itemtype="https://schema.org/FAQPage">
    <div class="md:flex gap-x-8 md:gap-x-16">
      <div class="md:w-1/3 py-4 md:px-4 text-sm">
        <h2 class="text-3xl md:text-4xl" itemprop="name">{{ zone.title }}</h2>
        <div v-html="zone.description" class="markdown my-4"></div>
      </div>

      <ul class="md:w-2/3">
        <li v-for="(faq, index) in zone.questions" :key="index" class="mb-2" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
          <button
            class="w-full text-left font-medium text-lg px-4 py-3 flex justify-between items-center text-gray-800 bg-body-gray-dark hover:bg-body-gray-darker rounded-sm"
            @click="toggleQuestion(index)"
            itemprop="name">
            {{ faq.question }}
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"
                 :class="{'rotate-180': openQuestionIndex === index}" stroke-width="2"
                 class="feather feather-chevron-down w-5 h-5 transition-transform">
              <polyline points="6 9 12 15 18 9"></polyline>
            </svg>
          </button>
          <!-- Answer section with FAQ schema -->
          <div v-show="openQuestionIndex === index"
               class="p-6 text-gray-800 mb-5 bg-body-gray-dark transition"
               itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
            <div itemprop="text" v-html="faq.answer"></div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: "FaqSection",
  props: ['zone'],
  data() {
    return {
      openQuestionIndex: -1, // No question is selected initially
    };
  },
  methods: {
    toggleQuestion(index) {
      // If the question is already open, close it, otherwise open the new one
      this.openQuestionIndex = this.openQuestionIndex === index ? -1 : index;
    },
  },
};
</script>

<style>
.rotate-180 {
  transform: rotate(180deg);
}
</style>
