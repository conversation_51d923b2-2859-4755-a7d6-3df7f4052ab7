<template>
  <div class="section-wrapper image xl:max-w-4xl container">
    <ResponsivePicture v-if="imageAttributes !== null"
                       :imgClass="'h-full'"
                       :isImgFullscreen="true"
                       :alt="alternativeText"
                       :caption="caption"
                       :smallURL="getImageOfSize(imageAttributes, 'small')"
                       :mediumURL="getImageOfSize(imageAttributes, 'medium')"
                       :largeURL="getImageOfSize(imageAttributes, 'large')"
                       :xlargeURL="getImageOfSize(imageAttributes, 'xlarge')"/>
  </div>
</template>

<script>
import ImageMeta from "@/mixins/imageMeta.js";

export default {
  name: "ImageSection",
  props: ['zone'],
  mixins: [ImageMeta],
  computed: {
    imageAttributes() {
      return this.getAttribute(this.zone, 'image.data.attributes');
    }
  },
}
</script>
