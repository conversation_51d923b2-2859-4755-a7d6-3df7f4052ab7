<template>
  <div class="section-wrapper text xl:max-w-4xl container">
    <h2 v-if="zone.title" class="text-3xl md:text-5xl mb-5 md:mb-8">{{ zone.title }}</h2>
    <div class="w-full flex items-center justify-between flex-col gap-x-8"
         :class="[zone.isColumn ? zone.imageFirst ? 'flex-col' : 'flex-col-reverse' : zone.imageFirst ? 'md:flex-row' : 'md:flex-row-reverse']">
      <div class="w-full flex-1" :class="[{ 'mb-4': zone.isColumn && zone.imageFirst}]">
        <div class="w-full relative mb-4 md:mb-0">
          <ResponsivePicture v-if="imageAttributes !== null"
                             :imgClass="'h-full m-auto'"
                             :isImgFullscreen="true"
                             :alt="alternativeText"
                             :caption="caption"
                             :smallURL="getImageOfSize(imageAttributes, 'small')"
                             :mediumURL="getImageOfSize(imageAttributes, 'medium')"
                             :largeURL="getImageOfSize(imageAttributes, 'large')"
                             :xlargeURL="getImageOfSize(imageAttributes, 'xlarge')"/>
        </div>
      </div>

      <div class="flex-1 text-left" :class="[{ 'mb-4': zone.isColumn && !zone.imageFirst}]">
        <div v-html="zone.content" class="markdown text-onvocado-dark-gray"></div>
      </div>
    </div>
  </div>
</template>

<script>
import ImageMeta from "@/mixins/imageMeta";

export default {
  name: "ImageTextSection",
  props: ['zone'],
  mixins: [ImageMeta],
  computed: {
    imageAttributes() {
      return this.getAttribute(this.zone, 'image.data.attributes');
    }
  },
}
</script>
