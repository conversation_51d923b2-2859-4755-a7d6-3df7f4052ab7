<template>
  <NuxtLink
    :to="`${$i18n.locale === $i18n.defaultLocale ? '' : '/' + $i18n.locale}/support/topics/${item.slug}`"
    :aria-label="$t('View support article')"
    class="support-link h-full block p-6 border rounded-lg bg-body-gray-dark hover:shadow-lg transition-shadow duration-300"
    :class="{ 'bg-gray-100': isActive }"
    @mouseenter="isActive = true"
    @mouseleave="isActive = false">
    <div class="h-full flex flex-col justify-between">
      <div>
        <h2 class="mb-2 text-xl font-semibold line-clamp-3">
          {{ item.title }}
        </h2>
        <p class="text-onvocado-gray-dark line-clamp-3">
          {{ item.count }} Article{{ item.count > 1 ? 's' : '' }}
        </p>
      </div>
    </div>
  </NuxtLink>
</template>

<script>
export default {
  name: "SupportCategoryOverviewItem",
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isActive: false,
    };
  },
};
</script>

<style lang="scss" module>
.link {
  &:hover {
    > div:first-child {
      @apply lg:rounded-[40px];
    }

    button {
      @apply lg:opacity-100;
    }
  }
}

.activeLink {
  > div:first-child {
    > div:first-child {
      @apply rounded-[40px];
    }
  }

  button {
    @apply opacity-100;
  }
}
</style>
