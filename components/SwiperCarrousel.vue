<template>
  <div class="relative">
    <!-- Slider main container -->
    <div class="swiper" ref="swiperElement" id="swiper-gallery">
      <!-- Additional required wrapper -->
      <div class="swiper-wrapper" :class="{'!items-start': isText}">
        <!-- Slides -->
        <div class="swiper-slide"
             :class="{'border-t-4 border-onvocado-primary rounded': isText}"
             v-for="(slide, index) in slides"
             :key="index">
          <!-- Dynamically use the slide component based on `isText` -->
          <component
            :is="isText ? 'SwiperCarrouselSlideText' : 'SwiperCarrouselSlide'"
            :slide="slide"
            :photoSwipeActive="photoSwipeActive"
            :fixedHeight="fixedHeight" />
        </div>
      </div>
      <!-- If we need pagination -->
      <!-- <div class="swiper-pagination"></div> -->

      <!-- Navigation buttons (conditionally rendered) -->
      <div v-if="showNavigation" class="swiper-button-prev"></div>
      <div v-if="showNavigation" class="swiper-button-next"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "SwiperCarrousel",
  props: {
    slides: {
      type: Array,
      default: () => [],
    },
    photoSwipeActive: {
      type: Boolean,
      default: false,
    },
    fixedHeight: {
      type: Boolean,
      default: false,
    },
    isText: {
      type: Boolean,
      default: false,
    },
    startAtEnd: {
      type: Boolean,
      default: false
    },
    showNavigation: {
      type: Boolean,
      default: false
    },
    customOptions: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      swiper: null,
      defaultSwiperOptions: {
        slidesPerView: "auto",
        spaceBetween: 10,
        slidesPerGroup: 1,
        slidesOffsetBefore: 16,
        slidesOffsetAfter: 14,
        loop: true,
        loopFillGroupWithBlank: false,
        autoplay: {
          delay: 3000, // 3 seconds
          disableOnInteraction: false, // Continue autoplay after user interactions
        },
        // Responsive breakpoints
        breakpoints: {
          320: {
            spaceBetween: 10,
            slidesPerView: 1.15,
            draggable: false,
          },
          768: {
            spaceBetween: 20,
            slidesPerView: "auto",
            slidesOffsetBefore: 40,
            slidesOffsetAfter: 20,
          },
          1220: {
            slidesPerView: "auto",
            spaceBetween: 20,
            slidesOffsetBefore: 40,
            slidesOffsetAfter: 40,
          },
          1280: {
            slidesPerView: "auto",
            spaceBetween: 20,
            slidesOffsetBefore: 40,
            slidesOffsetAfter: 20,
          },
          1500: {
            slidesPerView: "auto",
            spaceBetween: 20,
            slidesOffsetBefore: 40,
            slidesOffsetAfter: 40,
          },
        },
      },
    };
  },
  watch: {
    slides: {
      handler(newSlides) {
        this.$nextTick(() => {
          if (this.swiper && newSlides.length > 0 && this.startAtEnd) {
            this.swiper.slideTo(newSlides.length - 1, 0); // Move to last slide
          }
        });
      },
      deep: true,
      immediate: true // Runs on component mount
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initializeSwiper();
    });
  },
  methods: {
    initializeSwiper() {
      // Build a configuration object based on the default options, custom options,
      // and whether navigation is enabled.
      const swiperOptions = {
        ...this.defaultSwiperOptions,
        // Always include the Autoplay module.
        modules: [this.$swiperModules.Autoplay],
        ...this.customOptions,
      };

      // If navigation should be enabled, add the Navigation module and its config.
      if (this.showNavigation) {
        // Add the Navigation module.
        swiperOptions.modules.push(this.$swiperModules.Navigation);
        // Add navigation configuration.
        swiperOptions.navigation = {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        };
      }

      this.swiper = new this.$swiper(this.$refs.swiperElement, swiperOptions);

      // Ensure Swiper is ready before moving to last slide.
      setTimeout(() => {
        if (this.swiper && this.slides.length > 0 && this.startAtEnd) {
          this.swiper.slideTo(this.slides.length - 1, 0); // Move to last slide without animation
        }
      }, 300); // Delay to allow Swiper to fully render
    },
    triggerSlideCarrousel(directionLeft) {
      if (directionLeft) {
        this.swiper.slideNext();
      } else {
        this.swiper.slidePrev();
      }
    },
  },
};
</script>

<style>
:root {
    --swiper-theme-color: #0f2815;
}
</style>

<style lang="scss" scoped>
.swiper-container {
  width: 100%;
}

.swiper-wrapper {
  @apply w-full items-center;
}

.swiper-slide {
  text-align: center;
  width: fit-content;
  height: auto;
}
</style>
