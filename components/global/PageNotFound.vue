<template>
  <section class="my-24 md:my-36 flex flex-col justify-between">
    <div class="container flex flex-col justify-center items-center h-full">
      <div class="w-full md:w-2/4">
        <span>404</span>
        <h3 class="text-xl font-semibold m-0 mt-8">{{ $t('pageNotFound.title') }}</h3>
        <p class="text-lg mb-4">{{ $t('pageNotFound.description') }}</p>
        <NuxtLink :to="$localePathWithTrailingSlash('/')" class="button small text-[17px] inline-block" :aria-label="$t('pageNotFound.homeButtonLabel')">
          {{ $t('pageNotFound.homeButton') }}
        </NuxtLink>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'PageNotFound',
}
</script>
