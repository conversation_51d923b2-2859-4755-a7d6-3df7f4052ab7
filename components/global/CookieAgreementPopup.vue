<template>
  <transition name="fade">
    <div v-if="show"
         class="p-2 md:p-4 fixed left-2 md:left-4 right-2 md:right-auto bottom-2 md:bottom-4 bg-body-gray-dark md:bg-onvocado-gray-darker shadow-custom md:shadow-lg rounded-xl z-50">
      <div class="md:max-w-md mx-auto text-body-black md:text-body-gray">
        <div class="md:mb-4 flex justify-end">
          <button @click="hide" class="text-lg leading-none">&times;</button>
        </div>
        <p class="text-sm mb-2 md:mb-4 text-onvocado-gray-dark md:text-body-gray">
          {{ $t('cookiePopup.message') }}
        </p>
        <div v-if="detailedView" class="mb-4">
          <div>
            <label>
              <input type="checkbox" v-model="necessary" disabled>
              {{ $t('cookiePopup.necessary') }}
            </label>
          </div>
          <div>
            <label>
              <input type="checkbox" v-model="statistics">
              {{ $t('cookiePopup.statistics') }}
            </label>
          </div>
          <div>
            <label>
              <input type="checkbox" v-model="marketing">
              {{ $t('cookiePopup.marketing') }}
            </label>
          </div>
        </div>
        <div class="flex justify-between items-end">
          <NuxtLink
            class="underline"
            :to="$localePathWithTrailingSlash('/privacy-policy')"
            @click.native="hide">
            {{ $t('cookiePopup.cookiePolicy') }}
          </NuxtLink>
          <button
            class="w-full button md:w-auto px-3 text-xl font-medium md:px-3 md:text-onvocado-gray-darker"
            @click="detailedView ? accept() : acceptSimple()">
            {{ detailedView ? $t('cookiePopup.acceptSelected') : $t('cookiePopup.ok') }}
          </button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: "CookieAgreementPopup",
  data() {
    return {
      show: false,
      necessary: true,
      statistics: true,
      marketing: true
    }
  },
  computed: {
    detailedView() {
      return this.$route.query.detailedConsent === 'true';
    }
  },
  mounted() {
    const agreed = this.$cookies.get('agreed_on_cookie_usage')

    if (!agreed) {
      setTimeout(() => {
        this.show = true
      }, 1000)
    } else {
      this.updateGtagConsent(agreed);
    }
  },
  methods: {
    accept() {
      const consent = {
        preferences: this.necessary,
        statistics: this.statistics,
        marketing: this.marketing
      };

      this.$cookies.set('agreed_on_cookie_usage', consent, {
        maxAge: 60 * 60 * 24 * 30 // About 1 month.
      });

      this.show = false;
      this.updateGtagConsent(consent);
    },
    acceptSimple() {
      const consent = {
        necessary: true,
        statistics: true,
        marketing: true
      };

      this.$cookies.set('agreed_on_cookie_usage', consent, {
        maxAge: 60 * 60 * 24 * 30 // About 1 month.
      });

      this.show = false;
      this.updateGtagConsent(consent);
    },
    hide() {
      this.show = false
    },
    updateGtagConsent(consent) {
      if (process.env.NODE_ENV !== 'production') return;
      function gtag() {
        dataLayer.push(arguments);
      }

      gtag('consent', 'update', {
        'ad_storage': consent.marketing ? 'granted' : 'denied',
        'analytics_storage': consent.statistics ? 'granted' : 'denied'
      });
    }
  }
}
</script>
