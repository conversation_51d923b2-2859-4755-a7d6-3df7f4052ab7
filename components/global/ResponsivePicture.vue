<template>
  <figure class="w-full h-full">
    <picture>
      <source type="image/webp"
              :srcset="getSrcSet(true)"
              :sizes="getSizes()">
      <!--       img tag is a fallback when using a browser which doesn't support webp -->
      <img :src="smallURL"
           :srcset="getSrcSet()"
           :sizes="getSizes()"
           width="1920"
           height="1920"
           class="object-cover"
           :class="[ { 'h-full': !baseHeightOverride, 'w-full md:w-auto': baseHeightOverride }, imgClass ]"
           :id="imgID"
           :alt="alt">
    </picture>
    <figcaption v-if="caption" class="my-2.5 md:text-center text-sm">{{ caption }}</figcaption>
  </figure>
</template>

<script>
export default {
  name: "ResponsivePicture",
  props: {
    isImgFullscreen: {
      type: Boolean,
      required: false,
      default: false
    },
    imgID: {
      type: String,
      required: false,
      default: ''
    },
    imgClass: {
      type: String,
      required: false,
      default: ''
    },
    baseHeightOverride: {
      type: Boolean,
      required: false,
      default: false
    },
    alt: {
      type: String,
      default: "Image"
    },
    caption: {
      type: String,
      required: false,
    },
    smallURL: {
      type: String,
      required: true,
    },
    mediumURL: {
      type: String,
      required: false,
      default: ''
    },
    largeURL: {
      type: String,
      required: false,
      default: ''
    },
    xlargeURL: {
      type: String,
      required: false,
      default: ''
    },
  },
  mounted() {
    this.setupImageLoadedEvent();
  },
  methods: {
    setupImageLoadedEvent() {
      const imgElement = this.$el.querySelector('img');
      this.$imagesLoaded(imgElement, () => {
        this.$emit('image-loaded');
      });
    },
    getSizes() {
      // sizes="(max-width: 640px) 250px, 600px" if the screen size is smaller than 640px width -> serves 250px image;
      // if its larger -> serves the 600px
      if (this.isImgFullscreen) {
        return "(max-width: 320px) 250px," +
          "(max-width: 600px) 600px," +
          "(max-width: 1200px) 1000px," +
          "1920px";
      } else {
        return "(max-width: 640px) 250px," +
          "(max-width: 1240px) 600px," +
          "(max-width: 1920px) 1200px," +
          "1920px";
      }
    },
    getSrcSet(webp) {
      if (webp) {
        return this.toWebp(this.smallURL) + ' 250w, ' +
          (this.toWebp(this.mediumURL) || this.toWebp(this.smallURL)) + ' 600w, ' +
          (this.toWebp(this.largeURL) || this.toWebp(this.mediumURL) || this.toWebp(this.smallURL)) + ' 1200w, ' +
          (this.toWebp(this.xlargeURL) || this.toWebp(this.largeURL) || this.toWebp(this.mediumURL) || this.toWebp(this.smallURL)) + ' 1920w'
      } else {
        return this.smallURL + ' 250w, ' +
          (this.mediumURL || this.smallURL) + ' 600w, ' +
          (this.largeURL || this.mediumURL || this.smallURL) + ' 1200w, ' +
          (this.xlargeURL || this.largeURL || this.mediumURL || this.smallURL) + ' 1920w'
      }
    },
    toWebp(string) {
      return string.replace(/\.(jpg|png)/g, '.webp')
    }
  }
}
</script>
