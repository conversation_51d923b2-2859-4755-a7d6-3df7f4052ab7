<!--<template>-->
<!--  <Transition name="fade">-->
<!--    <div v-show="!loadingAnimationFinished" :class="$style.loader" class="w-screen h-screen fixed inset-0 flex bg-body-gray z-50">-->
<!--      <div v-if="!loadingAnimationFinished" ref="logo" :class="$style.loaderGIF">-->
<!--        <img v-if="gifSrcReady" ref="loaderImg" :src="'/logo-lusio.gif?' + timeNow()" alt="Onvocado loader">-->
<!--      </div>-->
<!--    </div>-->
<!--  </Transition>-->
<!--</template>-->

<!--<script>-->
<!--import { mapMutations } from 'vuex';-->

<!--export default {-->
<!--  name: "Loader",-->
<!--  data() {-->
<!--    return {-->
<!--      gifLoadingTime: process.env.NODE_ENV === 'production' ? 3300 : 0, // ToDo change back to 3300-->
<!--      domLoaded: false,-->
<!--      gifAnimationFinished: false,-->
<!--      gifSrcReady: false,-->
<!--    }-->
<!--  },-->
<!--  computed: {-->
<!--    loadingAnimationFinished() {-->
<!--      return this.$store.state.loadingAnimationFinished-->
<!--    },-->
<!--  },-->
<!--  mounted() {-->
<!--    // eslint-disable-next-line-->
<!--    // this.$refs['loaderImg'].src = '/logo-lusio.gif?' + this.timeNow;-->
<!--    this.gifSrcReady = true;-->

<!--    if (process.client) { // eslint-disable-line-->
<!--      if (document.readyState === 'complete') {-->
<!--        this.domLoaded = true;-->
<!--        this.triggerAnimationSequence();-->
<!--      } else {-->
<!--        window.addEventListener('load', () => {-->
<!--          this.domLoaded = true;-->
<!--          this.triggerAnimationSequence();-->
<!--        });-->
<!--      }-->
<!--    }-->
<!--    setTimeout(() => {-->
<!--      this.gifAnimationFinished = true;-->
<!--      this.triggerAnimationSequence();-->
<!--    }, this.gifLoadingTime);-->
<!--  },-->
<!--  methods: {-->
<!--    ...mapMutations({-->
<!--      finishLoading: 'finishLoading'-->
<!--    }),-->
<!--    triggerAnimationSequence() {-->
<!--      if (this.gifAnimationFinished && this.domLoaded) {-->
<!--        // eslint-disable-next-line-->
<!--        const logo = this.$refs['logo'];-->

<!--        // ToDo At this point switch the gif to an img so it stops looping-->

<!--        this.finishLoading();-->
<!--        // eslint-disable-next-line-->
<!--      }-->
<!--    },-->
<!--  }-->
<!--};-->
<!--</script>-->

<!--<style lang="scss" module>-->
<!--.loader {-->
<!--  align-items: center;-->
<!--  justify-content: space-around;-->
<!--  transition: all .2s ease-out;-->
<!--}-->

<!--.loaderGIF {-->
<!--  width: 60px;-->
<!--}-->
<!--</style>-->
