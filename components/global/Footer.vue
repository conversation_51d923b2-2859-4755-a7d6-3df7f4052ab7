<template>
  <footer :class="$style.footer">
    <div :class="$style.footerContainer">
      <div :class="$style.mail" class="text-body-gray">
        <OnvocadoLogo class="w-6 mb-6"/>
        <h2>Onvocado <br>{{ $t('Engage your users') }}</h2>
        <hr>
        <a href="mailto:<EMAIL>" target="_blank"><EMAIL></a>
      </div>

      <div :class="$style.actions">
        <ul class="menu-effect dark">
          <li>
            <NuxtLink class="font-bold" :to="$localePathWithTrailingSlash('/')" :data-hover="$t('Home')">{{
                $t('Home')
              }}
            </NuxtLink>
          </li>
          <li>
            <NuxtLink class="font-bold" :to="$localePathWithTrailingSlash('/product/')" :data-hover="$t('Product')">
              {{ $t('Product') }}
            </NuxtLink>
          </li>
          <li>
            <NuxtLink class="font-bold" :to="$localePathWithTrailingSlash('/blog/')" :data-hover="$t('Blog')">
              {{ $t('Blog') }}
            </NuxtLink>
          </li>
          <li>
            <NuxtLink class="font-bold" :to="$localePathWithTrailingSlash('/pricing/')" :data-hover="$t('Pricing')">
              {{ $t('Pricing') }}
            </NuxtLink>
          </li>
          <li>
            <NuxtLink class="font-bold" :to="$localePathWithTrailingSlash('/contact/')" :data-hover="$t('Contact')">
              {{ $t('Contact') }}
            </NuxtLink>
          </li>
        </ul>

        <ul class="menu-effect dark">
          <li>
            <NuxtLink :to="$localePathWithTrailingSlash('/support/')" :data-hover="$t('Support')">{{
                $t('Support')
              }}
            </NuxtLink>
          </li>
          <li>
            <NuxtLink :to="$localePathWithTrailingSlash('/integrations/')" :data-hover="$t('Integrations')">
              {{ $t('Integrations') }}
            </NuxtLink>
          </li>
          <NuxtLink :to="$localePathWithTrailingSlash('/roadmap/')" :data-hover="$t('Roadmap')">{{
                $t('Roadmap')
              }}
            </NuxtLink>
          <li>
            <NuxtLink :to="$localePathWithTrailingSlash('/terms/')" :data-hover="$t('Terms & Conditions')">
              {{ $t('Terms & Conditions') }}
            </NuxtLink>
          </li>
          <li>
            <NuxtLink :to="$localePathWithTrailingSlash('/privacy-policy/')" :data-hover="$t('Privacy Policy')">
              {{ $t('Privacy Policy') }}
            </NuxtLink>
          </li>
        </ul>
      </div>

      <div v-if="comparisons.length > 0" :class="$style.actions">
        <ul class="menu-effect dark">
          <li><h3 class="mx-auto mb-2 inline-block">{{ $t('compare') }}</h3></li>

          <li v-for="(competitor, index) in comparisons" :key="index" class="last:mb-4">
            <NuxtLink :to="$localePathWithTrailingSlash(competitor.href)"
                      :data-hover="competitor.title">
              {{ competitor.title }}
            </NuxtLink>
          </li>
        </ul>
      </div>

      <div :class="$style.socialContainer">
        <div id="waitlist" :class="$style.subscribe">
          <h3>{{ $t('Join the Newsletter') }}</h3>
          <MailchimpInput :btn-text="$t('Get Latest News')" :outline="true"/>
        </div>

        <div>
          <h3>{{ $t('Follow Us') }}</h3>
          <ul class="items-center">
            <li>
              <a :class="$style.socialLink"
                 href="https://facebook.com/onvocado"
                 target="_blank"
                 :title="$t('Facebook')"
                 rel="noopener">
                <FacebookIcon class="h-[22px]"/>
              </a>
            </li>
            <li>
              <a :class="$style.socialLink"
                 href="https://www.instagram.com/onvocado.app/"
                 target="_blank"
                 :title="$t('Instagram')"
                 rel="noopener">
                <InstagramIcon class="h-5"/>
              </a>
            </li>
            <li>
              <a :class="$style.socialLink"
                 href="https://linkedin.com/company/onvocado/"
                 target="_blank"
                 :title="$t('LinkedIn')"
                 rel="noopener">
                <LinkedInIcon class="h-5"/>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div :class="$style.footerContainer">
      <div :class="$style.copy">
        <span>&copy; Onvocado {{ year }} • {{ $t('Made in Europe') }}</span>
        <span v-if="showIllustrationCredits">{{ $t('Illustrations from') }} <a href="https://absurd.design/"
                                                                               target="_blank"
                                                                               rel="noopener">absurd design</a></span>
      </div>
    </div>
  </footer>
</template>

<script>
import OnvocadoLogo from "~/static/logo/logo_light.svg?inline";
import FacebookIcon from "~/static/icons/facebook.svg?inline";
import LinkedInIcon from "~/static/icons/linkedin.svg?inline";
import InstagramIcon from "~/static/icons/instagram.svg?inline";

export default {
  name: "Footer",
  async fetch() {
    await this.$store.dispatch('comparison/fetchComparisons', this.$i18n.locale || 'en');
  },
  components: {
    OnvocadoLogo, FacebookIcon, LinkedInIcon, InstagramIcon
  },
  computed: {
    year() {
      const now = new Date()
      return now.getFullYear()
    },
    comparisons() {
      return this.$store.state.comparison.comparisons[this.$i18n.locale] || [];
    },
  },
  watch: {
    '$i18n.locale'(newLocale) {
      this.$store.dispatch('comparison/fetchComparisons', newLocale);
    },
  },
  data() {
    return {
      showIllustrationCredits: false,
    };
  },
  created() {
    this.showIllustrationCredits = this.$route.name === "product___en" || this.$route.name === "product___bg"
  }
}
</script>

<style lang="scss" module>
.footer {
  @apply bg-body-black text-body-gray;

  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120px 20px 60px 20px;
  overflow: hidden;
}

.footerContainer {
  @apply gap-x-2;
  width: 100%;
  max-width: 1680px;
  padding: 0 80px;
  margin: 0 auto;
  display: flex;

  &:first-of-type {
    padding: 0 80px 120px 80px;
  }
}

.mail {
  flex: 1;

  span {
    @apply text-xl;
    font-weight: bold;
    color: #FFF;
  }

  hr {
    @apply border-onvocado-primary border;
    margin: 10px 0;
  }

  a {
    color: #FFF;
    font-weight: 300;
  }
}

.actions {
  flex: 2;

  ul {
    width: 200px;
    margin: 0 auto;
    list-style: none;

    &:first-of-type {
      margin-bottom: 20px;
    }
  }

  li {
    a {
      color: white;
      text-decoration: none;
    }
  }
}

.socialContainer {
  @apply text-body-gray;

  display: flex;
  flex-direction: column;
  margin: 0 0 0 auto;

  h3 {
    margin-bottom: 10px;
  }

  ul {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
  }

  li {
    margin: 0 10px;

    &:first-of-type {
      margin: 0 10px 0 0;
    }
  }

  .socialLink {
    text-decoration: none;

    svg {
      fill: rgba(#f8f9e8, .95);
      transition: fill .2s ease;

      &:hover {
        fill: #f8f9e8;
      }
    }

    path {
      fill: rgba(#f8f9e8, .95);
    }
  }
}

.subscribe {
  margin-bottom: 20px;
}

.copy {
  width: 100%;
  display: flex;
  justify-content: space-between;

  span {
    @apply text-xs;
    font-weight: 400;
    color: #abb1b9;
  }

  a {
    color: #FFF;
  }
}

@media (max-width: 768px) {
  .actions {
    margin: 60px 0 0 0;

    ul {
      margin: 0;
    }

    li {
      margin-bottom: 6px;
    }
  }

  .footerContainer {
    flex-direction: column;
    padding: 0;

    &:first-of-type {
      padding: 0 0 60px 0;
    }
  }

  .socialContainer {
    margin: 60px 0 0 0;

    li {
      margin: 0 20px;

      &:first-of-type {
        margin: 0 5px;
      }

      &:last-of-type {
        margin: 0 0 0 5px;
      }
    }
  }

  .subscribe {
    margin-bottom: 60px;
  }

  .copy {
    flex-direction: column;
  }
}
</style>
