<template>
  <div
    class="w-full h-18 fixed top-0 left-0 bg-body-gray dark:bg-body-black text-black z-30 shadow-nav dark:shadow-none">
    <div class="w-full h-full px-4 md:px-6 lg:px-8 flex dark:text-onvocado-primary">

      <nav id="header-nav"
           role="navigation"
           class="header-nav w-full py-2 hidden md:flex items-center justify-between">
        <div class="justify-start z-10">
          <NuxtLink :to="$localePathWithTrailingSlash('index')"
                    class="header__link-logo block align-middle"
                    :aria-label="$t('Home')">
            <Logo/>
          </NuxtLink>
        </div>

        <!-- Main Menu -->
        <!-- Desktop Menu -->
        <ul class="flex justify-center will-change-transform transition-all duration-300 menu-effect"
            :class="{ 'hidden': !showNav }">
          <li v-for="(page) in pages.data" :key="page.id"
              class="mr-3 lg:mr-5 last:mr-0 inline relative transition-colors duration-300 font-medium">
            <div class="relative group">
              <NuxtLink
                :class="[page.attributes.children ? 'has-children !flex items-center' : '', {'pointer-events-none': !page.attributes.href}]"
                :to="$localePathWithTrailingSlash(page.attributes.href)"
                :aria-label="page.attributes.children ? '' : $t(page.attributes.title)"
                :data-hover="page.attributes.children ? '' : $t(page.attributes.title)">
                {{ $t(page.attributes.title) }}
                <svg v-if="page.attributes.children"
                     class="w-4 mt-0.5 ml-1 rotate-90 fill-body-black dark:fill-body-gray"
                     xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 -960 960 960">
                  <path d="m321-80-71-71 329-329-329-329 71-71 400 400L321-80Z"/>
                </svg>
              </NuxtLink>
              <!-- Submenu for Desktop -->
              <template v-if="page.attributes.children">
                <ul
                  :class="$style.submenu"
                  class="min-w-96 absolute top-10 -left-10 hidden group-hover:block pt-3 pb-6 px-6 bg-onvocado-white text-black border-2 border-body-black rounded-xl">
                  <li class="py-1 mb-4 border-b-2 border-onvocado-primary-dark">
                    <span class="text-body-black">{{ $t(page.attributes.title) }}</span>
                  </li>
                  <li v-for="(child, index) in page.attributes.children" :key="index" class="py-2 flex">
                    <NuxtLink :to="$localePathWithTrailingSlash(child.href)"
                              :aria-label="$t(child.title)"
                              :data-hover="$t(child.title)"
                              class="!text-body-black transition-colors duration-300">{{ $t(child.title) }}
                    </NuxtLink>
                  </li>
                </ul>
              </template>
            </div>
          </li>
        </ul>

        <div class="flex justify-end transition-all duration-300 menu-effect items-center lg:gap-2"
             :class="{ 'hidden': !showNav }">
          <div class="mr-4 font-medium flex items-center capitalize">
            <NuxtLink
              class="mr-3 transition-colors !hidden lg:!block"
              :to="$localePathWithTrailingSlash('contact')"
              :aria-label="$t('Contact')"
              :data-hover="$t('Contact')">
              {{ $t('Contact') }}
            </NuxtLink>
            <LanguageSwitch/>
          </div>
          <div class="flex gap-2 justify-center items-center">
            <a href="https://app.onvocado.com/login"
               class="!text-[16px] text-body-black font-medium transition-colors"
              :aria-label="$t('Sign in')"
              :data-hover="$t('Sign in')">
              {{ $t('Sign in') }}</a>
            <a href="https://app.onvocado.com/register"
               class="button dark:hover:text-body-black dark:hover:bg-onvocado-primary-dark small !text-[16px]">
              {{ $t('Sign up') }}</a>
          </div>
        </div>
      </nav>

      <!-- Mobile Navigation Toggle -->
      <div class="w-full py-2 ml-auto flex justify-between items-center md:hidden z-40">
        <div>
          <NuxtLink :to="$localePathWithTrailingSlash('index')"
                    class="header__link-logo"
                    :aria-label="$t('Home')">
            <img :src="getImageSrc"
                 :class="[{ 'is-parked': loadingAnimationFinished }]"
                 :alt="$t('Onvocado logo')"
                 width="40"
                 height="40"/>
          </NuxtLink>
        </div>

        <div class="flex flex-row">
          <a href="https://app.onvocado.com/register" class="button small text-[17px]">{{ $t('Get started now') }}</a>
          <div class="flex items-center justify-center" @click="triggerMobileNavAnimation()">
            <Burger class="w-9 ml-4 relative flex z-40" :menu-open="toggleMobileMenu"/>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div ref="menuOverlay" class="w-full fixed inset-0 flex md:hidden z-30 translate-x-full">
      <nav
        class="w-full h-full relative flex flex-col justify-center overflow-x-hidden items-center bg-body-black text-onvocado-white delay-100">
        <ul class="w-full mt-20 mb-2 flex flex-col justify-center flex-grow capitalize overflow-auto"
            :class="{ 'pt-44': openSubmenus.length > 0 }">
          <li class="ml-8" :class="[ openSubmenus.length > 0 ? 'overflow-none' : 'overflow-hidden' ]">
            <NuxtLink
              class="menu-link mb-2 text-5xl inline-block translate-y-0"
              :to="$localePathWithTrailingSlash('index')"
              :aria-label="$t('Home')">
              {{ $t('Home') }}
            </NuxtLink>
          </li>

          <!-- Mobile Menu Items -->
          <li v-for="page in pages.data" :key="page.id" class="ml-8"
              :class="[ openSubmenus.length > 0 ? 'overflow-none' : 'overflow-hidden' ]">
            <component
              :is="page.attributes.children ? 'button' : 'NuxtLink'"
              :to="$localePathWithTrailingSlash(!page.attributes.children ? page.attributes.href : undefined)"
              @click="toggleSubmenu(page.id)"
              :class="[
                  'menu-link mb-2 inline-block translate-y-0 text-2xl',
                  {
                    'text-5xl': page.prioritize,
                    'mt-4': page.addMargin,
                    'pointer-events-none': !page.attributes.href && !page.attributes.children,
                    'flex items-center gap-2': page.attributes.children,
                  }
              ]"
              :aria-label="$t(page.attributes.children ? page.attributes.title : page.attributes.title)"
              :aria-expanded="page.attributes.children ? isSubmenuOpen(page.id) : undefined"
              :data-hover="!page.attributes.children ? $t(page.attributes.title) : undefined">
              {{ $t(page.attributes.title) }}
              <svg
                v-if="page.attributes.children"
                class="w-4 mt-0.5 ml-1 rotate-90 fill-body-gray transition-transform duration-200"
                :class="{ 'rotate-0': isSubmenuOpen(page.id) }"
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 -960 960 960">
                <path d="m321-80-71-71 329-329-329-329 71-71 400 400L321-80Z"/>
              </svg>
            </component>

            <!-- Submenu for Mobile -->
            <ul v-if="isSubmenuOpen(page.id)" class="ml-6">
              <li v-for="(child, index) in page.attributes.children" :key="index" class="last:mb-4">
                <NuxtLink :to="$localePathWithTrailingSlash(child.href)"
                          class="menu-link mb-2 inline-block translate-y-0 text-2xl">
                  {{ $t(child.mobileTitle) }}
                </NuxtLink>
              </li>
            </ul>
          </li>
          <li class="ml-8" :class="[ openSubmenus.length > 0 ? 'overflow-none' : 'overflow-hidden' ]">
            <NuxtLink
              class="menu-link mb-1 text-2xl inline-block translate-y-0"
              :to="$localePathWithTrailingSlash('contact')"
              :aria-label="$t('Contact')">
              {{ $t('Contact') }}
            </NuxtLink>
          </li>
          <!-- Language Switcher for Mobile -->
          <li class="ml-8" :class="[ openSubmenus.length > 0 ? 'overflow-none' : 'overflow-hidden' ]">
            <button
              @click="toggleLanguageMenu"
              class="menu-link mb-2 flex translate-y-0 text-2xl items-center gap-1 capitalize"
              :aria-expanded="isLanguageMenuOpen"
              :aria-label="$t('Language')">
              {{ $t('Language') }}
              <svg
                class="w-3 mt-0.5 ml-1 rotate-90 fill-body-gray transition-transform duration-200"
                :class="{ 'rotate-0': isLanguageMenuOpen }"
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 -960 960 960">
                <path d="m321-80-71-71 329-329-329-329 71-71 400 400L321-80Z"/>
              </svg>
            </button>

            <!-- Language Options -->
            <ul v-if="isLanguageMenuOpen" class="ml-6">
              <li v-for="locale in availableLocales" :key="locale.code" class="last:mb-4">
                <NuxtLink :to="switchLocalePathWithTrailingSlash(locale.code) + '/'"
                          class="menu-link mb-2 inline-block translate-y-0 text-2xl">
                  {{ locale.code }}
                </NuxtLink>
              </li>
            </ul>
          </li>
        </ul>
        <div class="w-full h-full max-h-44 mt-auto px-8 pb-7 flex flex-col justify-between text-center">
          <div class="button white outline">
            <a href="https://app.onvocado.com/">{{ $t('Sign in') }}</a>
          </div>
          <div class="button white !text-body-black">
            <a href="https://app.onvocado.com/register">{{ $t('Sign up') }}</a>
          </div>
          <div class="text-sm opacity-50">
            <p>&copy; Onvocado {{ year }}</p>
          </div>
        </div>
      </nav>
    </div>

  </div>
</template>

<script>
export default {
  name: "Menu",
  async fetch() {
    await this.$store.dispatch('useCases/fetchUseCases', this.$i18n.locale || 'en');
  },
  fetchOnServer: true,
  data() {
    return {
      toggleMobileMenu: false,
      isLanguageMenuOpen: false,
      year: null,
      openSubmenus: [],
    };
  },
  computed: {
    useCasesChildren() {
      return this.$store.state.useCases.useCases[this.$i18n.locale] || [];
    },
    availableLocales() {
      return this.$i18n.locales.filter(locale => locale.code !== this.$i18n.locale);
    },
    pages() {
      return {
        data: [
          {id: 1, attributes: {title: 'Product', href: '/product/'}, prioritize: true},
          {
            id: 2,
            attributes: {
              title: 'Use Cases',
              href: '',
              children: this.useCasesChildren,
            },
            prioritize: true,
          },
          {id: 3, attributes: {title: 'Templates', href: '/templates/'}, prioritize: true},
          {id: 4, attributes: {title: 'Blog', href: '/blog/'}, prioritize: true},
          {id: 5, attributes: {title: 'Pricing', href: '/pricing/'}, prioritize: false, addMargin: true},
        ],
      };
    },
    loadingAnimationFinished() {
      return this.$store.state.loadingAnimationFinished;
    },
    showNav() {
      return this.$store.state.showNav;
    },
    getImageSrc() {
      if (this.$colorMode.value === 'dark') {
        return '/icon_white.png';
      } else {
        return '/icon.png';
      }
    },
  },
  watch: {
    '$route'(to, from) {
      if (this.toggleMobileMenu) {
        this.triggerMobileNavAnimation(true);
      }
      this.$store.commit('setShowNav', true);
    },
    '$i18n.locale'(newLocale) {
      this.$store.dispatch('useCases/fetchUseCases', newLocale);
    },
  },
  mounted() {
    const now = new Date();
    this.year = now.getFullYear();
    this.initializeMenuOverlayAnimation();
    this.initializeMenuLinksAnimation();
  },
  methods: {
    toggleLanguageMenu() {
      this.isLanguageMenuOpen = !this.isLanguageMenuOpen;
    },
    initializeMenuOverlayAnimation() {
      // Anime js requires initial state to be set
      this.$anime.set(this.$refs['menuOverlay'], {
        translateX: '100%',
      });
    },
    initializeMenuLinksAnimation() {
      // Anime js requires initial state to be set
      const menuLinks = document.querySelectorAll('.menu-link');
      menuLinks.forEach((link) => {
        this.$anime.set(link, {
          translateY: '120%',
          opacity: 0,
        });
      });
    },
    triggerMobileNavAnimation(close = false) {
      this.toggleMobileMenu = !this.toggleMobileMenu;

      if (this.toggleMobileMenu && !close) {
        document.body.classList.add('is-nav-open');
        this.menuOverlayAnimation(true);
        this.menuLinksAnimation(true);
      } else {
        this.menuOverlayAnimation(false);
        this.menuLinksAnimation(false);
        document.body.classList.remove('is-nav-open');
        // Close all open submenus when the mobile menu is closed
        this.openSubmenus = [];
      }
    },
    menuOverlayAnimation(show) {
      this.$anime({
        targets: this.$refs['menuOverlay'],
        translateX: show ? '0%' : '100%',
        duration: 500,
        easing: 'easeInOutQuad',
      });
    },
    menuLinksAnimation(show) {
      this.$anime({
        targets: '.menu-link',
        translateY: show ? '0%' : '120%',
        opacity: show ? 1 : 0,
        duration: 400,
        easing: 'easeInOutQuad',
        delay: this.$anime.stagger(60, {start: show ? 500 : 0}),
      });
    },
    // handleLinkClick(page) {
    //   if (page.attributes.children) {
    //     this.toggleSubmenu(page.id);
    //   } else {
    //     this.triggerMobileNavAnimation();
    //   }
    // },
    toggleSubmenu(id) {
      const index = this.openSubmenus.indexOf(id);
      if (index > -1) {
        this.openSubmenus.splice(index, 1);
      } else {
        this.openSubmenus.push(id);
      }
    },
    isSubmenuOpen(id) {
      return this.openSubmenus.includes(id);
    },
  },
};
</script>

<style lang="scss" module>
.submenu {
  &::before {
    content: "";
    height: 20px;
    position: absolute;
    width: 100%;
    margin-top: -34px;
    margin-left: 0;
    margin-right: 0;
    left: 0;
    right: 0;
  }
}
</style>
