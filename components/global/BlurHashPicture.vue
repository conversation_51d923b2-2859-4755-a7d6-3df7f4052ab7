<template>
  <canvas ref="canvas" width="32" height="32" class="w-full h-full"></canvas>
</template>

<script>
export default {
  name: "BlurHashPicture",
  props: {
    hash: {
      type: String,
      required: true
    },
  },
  mounted() {
    this.setBlurHashColor();
    this.decodeAndDraw();
  },
  methods: {
    async decodeAndDraw() {
      this.pixels = this.$blurhashDecode(this.hash, 32, 32);
      this.initialize();
    },
    async setBlurHashColor() {
      const rgbAverageColor = this.$getBlurHashAverageColor(this.hash);
      if (this.$refs.canvas) {
        this.$refs.canvas.style.backgroundColor = `rgb(${rgbAverageColor.join(',')})`;
      }
    },
    initialize() {
      requestAnimationFrame(() => {
        const canvas = this.$refs.canvas;
        if (!canvas) return;

        const ctx = canvas.getContext("2d");
        const imageData = new ImageData(this.pixels, 32, 32);
        ctx.putImageData(imageData, 0, 0);
      });
    }
  }
}
</script>
