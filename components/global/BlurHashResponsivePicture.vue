<template>
  <div class="relative">
    <BlurHashPicture v-if="!blurHashRemoved && image && image.attributes?.blurhash" class="absolute inset-0 -z-10" :hash="image.attributes.blurhash"/>
    <ResponsivePicture v-if="imageAttributes !== null"
                       :class="['transition-opacity duration-500',
                          {'opacity-0': !imageLoaded}]"
                       :imgClass="'h-full aspect-16/9 ' + imgClass"
                       :alt="image.attributes?.alternativeText"
                       :smallURL="getImageOfSize(imageAttributes, 'small')"
                       :mediumURL="getImageOfSize(imageAttributes, 'medium')"
                       :largeURL="getImageOfSize(imageAttributes, 'large')"
                       :xlargeURL="getImageOfSize(imageAttributes, 'xlarge')"
                       @image-loaded="onImageLoaded"/>
  </div>
</template>

<script>
import ImageMeta from "@/mixins/imageMeta.js";

export default {
  name: "BlurHashResponsivePicture",
  mixins: [ImageMeta],
  props: {
    image: {
      type: Object,
      required: true
    },
   imgClass: {
      type: String,
      required: false,
      default: ''
    },
  },
  computed: {
    imageAttributes() {
      if (!this.image || !this.image.attributes) {
        return null;
      }
      return this.getAttribute(this.image, 'attributes');
    }
  },
  data() {
    return {
      imageLoaded: false,
      blurHashRemoved: false
    };
  },
  methods: {
    onImageLoaded() {
      this.imageLoaded = true;
      // Remove the BlurHashPicture after fade animation finished
      setTimeout(() => {
        this.blurHashRemoved = true;
      }, 500);
    }
  },
}
</script>
