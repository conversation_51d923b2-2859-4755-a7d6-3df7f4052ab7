<!-- VideoPlayer.vue -->
<template>
  <div class="rounded-lg md:rounded-xl overflow-hidden">
    <video ref="videoPlayer" class="w-full video-js vjs-fluid"></video>
  </div>
</template>

<script>
import videojs from 'video.js';

export default {
  name: 'VideoPlayer',
  props: {
    options: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      player: null
    }
  },
  mounted() {
    this.player = videojs(this.$refs.videoPlayer, this.options);
  },
  methods: {
    play() {
      if (this.player) {
        this.player.play();
      }
    }
  },
  beforeDestroy() {
    if (this.player) {
      this.player.dispose();
    }
  }
}
</script>
