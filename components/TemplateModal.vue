<template>
  <div class="my-auto">
    <!-- Modal Background -->
    <div
      class="fixed inset-0 bg-body-black bg-opacity-75 transition-opacity z-40"
      aria-hidden="true"></div>

    <!-- Use Template Button -->
    <div
      class="w-full h-16 fixed md:hidden bottom-0 left-0 right-0 flex space-x-4 justify-center items-center bg-body-gray border-t z-[55]">
      <a :href="`https://app.onvocado.com/register?templateId=${template.id}`" class="button secondary">
        Use this template
      </a>
    </div>

    <!-- Modal Container -->
    <div class="fixed inset-0 z-50">
      <div class="flex items-center justify-center min-h-screen md:pt-4 md:px-4 md:pb-20 text-center sm:p-0"
           @click="$emit('close')">

        <!-- Modal Content -->
        <div
          class="max-h-screen lg:max-h-[calc(100vh-110px)] overflow-none md:overflow-y-auto relative bg-body-gray rounded-xl text-left shadow-xl transform transition-all w-full max-w-6xl"
          data-lenis-prevent
          @click.stop>

          <!-- Modal Controls -->
          <div class="md:px-4 my-4 flex justify-between container">
            <div>
              <div class="flex gap-8 items-center">
                <NuxtLink
                  :to="`${$i18n.locale === $i18n.defaultLocale ? '' : '/' + $i18n.locale}${i18nPages['templates/_slug'][$i18n.locale].replace(':slug', template.attributes?.slug)}`">
                  <svg xmlns="http://www.w3.org/2000/svg" height="26px" viewBox="0 -960 960 960" width="26px"
                       fill="#0f2815">
                    <path d="M144-144v-288h72v165l477-477H528v-72h288v288h-72v-165L267-216h165v72H144Z"/>
                  </svg>
                </NuxtLink>

                <hr class="w-0.5 h-6 bg-body-black opacity-30">

                <div class="flex items-center gap-2">
                  <button :class="{'opacity-30 pointer-events-none': currentIndex === 0 }"
                          :disabled="currentIndex === 0"
                          type="button"
                          @click="changeTemplate(currentIndex - 1 )">
                    <svg xmlns="http://www.w3.org/2000/svg" height="26px" viewBox="0 -960 960 960" width="26px"
                         fill="#0f2815">
                      <path d="m313-440 224 224-57 56-320-320 320-320 57 56-224 224h487v80H313Z"/>
                    </svg>
                  </button>
                  <button type="button"
                          :class="{'opacity-30 pointer-events-none': (currentIndex + 1) === totalTemplates }"
                          :disabled="currentIndex === totalTemplates"
                          @click="changeTemplate(currentIndex + 1 )">
                    <svg xmlns="http://www.w3.org/2000/svg" height="26px" viewBox="0 -960 960 960" width="26px"
                         fill="#0f2815">
                      <path d="M630-444H192v-72h438L429-717l51-51 288 288-288 288-51-51 201-201Z"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- Modal Close Button -->
            <button
              type="button"
              class="text-gray-400 hover:text-gray-500"
              @click="$emit('close')">
              <span class="sr-only">Close</span>
              <svg xmlns="http://www.w3.org/2000/svg" height="26px" viewBox="0 -960 960 960" width="26px"
                   fill="#0f2815">
                <path
                  d="m291-240-51-51 189-189-189-189 51-51 189 189 189-189 51 51-189 189 189 189-51 51-189-189-189 189Z"/>
              </svg>
            </button>
          </div>

          <!-- Modal Body -->
          <div
            class="max-w-5xl max-h-[calc(100vh-62px)] md:max-h-none pt-5 pb-12 sm:p-6 mx-auto overflow-y-auto md:overflow-none">
            <div class="mb-16 container">
              <!-- Header -->
              <div class="flex justify-between items-end md:items-center">
                <!-- Title -->
                <h1 v-if="template.attributes.title" class="w-3/4 md:w-1/2 text-3xl lg:text-4xl 2xl:text-5xl font-bold">
                  {{ template.attributes.title }}
                </h1>

                <!-- Buttons -->
                <div class="w-1/2 hidden md:flex space-x-4 justify-end items-center">
                  <a :href="`https://app.onvocado.com/register?templateId=${template.id}`" class="button secondary">
                    Use this template
                  </a>
                </div>

              </div>

              <!-- Template Preview and Description Section -->
              <div class="grid grid-cols-12 gap-6 mt-6">
                <!-- Template Preview Section -->
                <div class="col-span-12 sm:col-span-7 flex flex-col">
                  <!-- Large Preview using WidgetStepPreview -->
                  <div
                    class="w-full relative shadow-2xl rounded-lg border bg-body-gray-dark aspect-16/10 overflow-hidden origin-top-left">
                    <WidgetStepPreview
                      v-if="template.steps && template.steps.length"
                      :widget="fakeTemplate"
                      :target-step-index="targetImgIndex"
                      class="w-full"/>
                  </div>

                  <section class="p-2 md:p-4 lg:p-6 grid grid-cols-4 gap-2 md:gap-4">
                    <button
                      v-for="(img, index) in template.attributes.thumbnails.data"
                      :key="index"
                      class="relative block overflow-hidden rounded-lg shadow-md hover:shadow-lg bg-body-gray-dark"
                      @click="setTargetImage(index)"
                      :class="{'border-2 border-onvocado-primary': targetImgIndex === index}">
                       <div class="w-full relative bg-body-gray-dark aspect-16/10 overflow-hidden origin-top-left">
                        <WidgetStepPreview :widget="fakeTemplate"
                                           :target-step-index="index"
                                           class="w-full"/>
                      </div>
                    </button>
                  </section>
                </div>

                <!-- Description & Buttons -->
                <div class="col-span-12 sm:col-span-5">
                  <!-- About the Template -->
                  <div class="text-gray-700 mb-4 markdown" v-html="template.attributes.content">
                  </div>

                  <!-- Template Info -->
                  <div class="text-sm mb-4">
                    <h4 class="font-semibold">Categories</h4>
                    <ul v-for="(category, index) in template.attributes.template_categories.data"
                        class="text-gray-500"
                        :key="index">
                      <li>{{ category.attributes.title }}</li>
                    </ul>
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import WidgetStepPreview from '@/libs/widget-step-preview.common.js'
import '@/libs/widget-step-preview.css'
import {i18nPages} from '@/i18n.config.js'

export default {
  name: "TemplateModal",
  props: {
    template: {
      type: Object,
      required: true,
    },
    totalTemplates: {
      type: Number,
      required: true,
    },
    currentIndex: {
      type: Number,
      required: true,
    },
  },
  components: {
    WidgetStepPreview,
  },
  data() {
    return {
      targetImgIndex: 0, // Start with the first image
      i18nPages,
      // ToDo #templateImpro remove this and make use of the template props
      fakeTemplate: {
        "id": 2,
        "name": "Sign Up bar",
        "type": "stickyBar",
        "tag": "sticky bar",
        "categories": [
          "lead-generation",
          "coupons-offers"
        ],
        "industries": [],
        "template_theme": "default",
        "description": "A sleek sticky bar designed to drive lead generation by encouraging users to sign up for exclusive coupons and offers.",
        "priority": 10,
        "steps": [
          {
            "id": "1p37o3m",
            "contentItems": [
              {
                "id": "bys5iof",
                "type": "headline",
                "column": "A",
                "content": {
                  "text": "SIGN UP FOR 20% OFF!"
                },
                "styling": {
                  "color": "rgba(254, 254, 254, 1)",
                  "fontSize": "48px",
                  "textAlign": "left",
                  "fontFamily": "Ubuntu Mono",
                  "fontWeight": "bold"
                },
                "category": "basic",
                "settings": {
                  "usesThemeStyles": true
                }
              },
              {
                "id": "p3nsf5x",
                "type": "headline",
                "column": "B",
                "content": {
                  "text": "I am a headline"
                },
                "styling": {
                  "color": "rgba(24, 23, 26, 1)",
                  "fontSize": "22px",
                  "textAlign": "center",
                  "fontFamily": "Roboto",
                  "fontWeight": "normal"
                },
                "category": "basic",
                "settings": {
                  "usesThemeStyles": true
                }
              },
              {
                "id": "4j9iuom",
                "type": "field",
                "column": "B",
                "content": {
                  "placeholder": "Email"
                },
                "styling": {
                  "color": "rgba(13, 13, 13, 1)",
                  "fontSize": "20px",
                  "textAlign": "left",
                  "fontFamily": "Lato",
                  "fontWeight": "400",
                  "borderColor": "rgba(0, 0, 0, 0.3)",
                  "borderWidth": "0px",
                  "borderRadius": "8px",
                  "backgroundColor": "rgba(254, 254, 254, 1)"
                },
                "category": "form",
                "settings": {
                  "type": "email",
                  "label": "Email address",
                  "unique": false,
                  "required": true,
                  "usesThemeStyles": true
                }
              },
              {
                "id": "pp2svgg",
                "type": "button",
                "column": "B",
                "content": {
                  "text": "GET DISCOUNT"
                },
                "styling": {
                  "color": "rgba(242, 248, 248, 1)",
                  "fontSize": "18px",
                  "fontFamily": "Lato",
                  "fontWeight": "bold",
                  "borderColor": "rgba(242, 248, 248, 1)",
                  "borderWidth": "0",
                  "borderRadius": "2px",
                  "justifyContent": "center",
                  "backgroundColor": "rgba(20, 20, 20, 0)"
                },
                "category": "basic",
                "settings": {
                  "href": "",
                  "action": "next",
                  "backBtn": false,
                  "usesThemeStyles": true
                }
              }
            ]
          },
          {
            "id": "xop4ymt",
            "contentItems": [
              {
                "id": "giyh3je",
                "type": "headline",
                "column": "A",
                "content": {
                  "text": "ENJOY YOUR 50% OFF"
                },
                "styling": {
                  "color": "rgba(254, 254, 254, 1)",
                  "fontSize": "48px",
                  "textAlign": "center",
                  "fontFamily": "Ubuntu Mono",
                  "fontWeight": "bold"
                },
                "category": "basic",
                "settings": {
                  "usesThemeStyles": true
                }
              },
              {
                "id": "3r8w12c",
                "type": "spacer",
                "column": "A",
                "content": {},
                "styling": {
                  "color": "rgba(0, 0, 0, 0)",
                  "padding": "10px"
                },
                "category": "other",
                "settings": {
                  "usesThemeStyles": true
                }
              },
              {
                "id": "uxtlici",
                "type": "voucher",
                "column": "A",
                "content": {
                  "text": "VOUCHER CODE"
                },
                "styling": {
                  "color": "rgba(254, 254, 254, 1)",
                  "fontSize": "36px",
                  "textAlign": "center",
                  "fontFamily": "Ubuntu Mono",
                  "fontWeight": "bold",
                  "borderColor": "rgba(254, 254, 254, 1)",
                  "borderWidth": "5px",
                  "borderRadius": "8px"
                },
                "category": "basic",
                "settings": {
                  "label": "Voucher",
                  "usesThemeStyles": true
                }
              },
              {
                "id": "505bm5u",
                "type": "text",
                "column": "A",
                "content": {
                  "text": "An email with the best deals and savings is coming soon!"
                },
                "styling": {
                  "color": "rgba(254, 254, 254, 1)",
                  "fontSize": "20px",
                  "textAlign": "center",
                  "fontFamily": "Lato",
                  "fontWeight": "400"
                },
                "category": "basic",
                "settings": {
                  "usesThemeStyles": true
                }
              },
              {
                "id": "33pwfwb",
                "type": "spacer",
                "column": "A",
                "content": {},
                "styling": {
                  "color": "rgba(0, 0, 0, 0)",
                  "padding": "14px"
                },
                "category": "other",
                "settings": {
                  "usesThemeStyles": true
                }
              },
              {
                "id": "48nrxyv",
                "type": "button",
                "column": "A",
                "content": {
                  "text": "CONTINUE SHOPPING"
                },
                "styling": {
                  "color": "rgba(242, 248, 248, 1)",
                  "fontSize": "18px",
                  "fontFamily": "Lato",
                  "fontWeight": "bold",
                  "borderColor": "rgba(242, 248, 248, 1)",
                  "borderWidth": "0",
                  "borderRadius": "2px",
                  "justifyContent": "center",
                  "backgroundColor": "rgba(0, 0, 0, 0)"
                },
                "category": "basic",
                "settings": {
                  "href": "",
                  "action": "close",
                  "backBtn": false,
                  "usesThemeStyles": true
                }
              }
            ]
          }
        ],
        "settings": {
          "trigger": "auto",
          "position": "topCenter",
          "animation": "fade",
          "hideMobile": false,
          "ribbonText": "",
          "ribbonType": "gift",
          "closeButton": true,
          "hideOverlay": false,
          "ribbonColor": "#000000",
          "displayCount": 2,
          "trafficShare": 0.5,
          "ribbonBgColor": "#000000",
          "ribbonPosition": "bottomLeft",
          "selectedCities": [],
          "triggerSeconds": 3,
          "closeButtonColor": "rgb(242, 248, 248)",
          "displayFrequency": 1,
          "locationStrategy": "SHOW_ALL",
          "triggerClickType": "",
          "selectedCountries": [],
          "triggerClickTarget": "",
          "closeOnOverlayClick": false,
          "triggerScrollPercent": 20,
          "displayFrequencyPeriod": "hour"
        },
        "styling": {
          "maxWidth": "100vw",
          "borderRadius": 0,
          "backgroundSize": "cover",
          "backgroundColor": "rgb(88,182,179)",
          "backgroundImage": "",
          "backgroundPosition": "center"
        },
        "theme": {
          "rank": {
            "baseColor": "rgba(208, 208, 208, 1)",
            "fontFamily": "Roboto"
          },
          "text": {
            "color": "rgba(0, 0, 0, 1)",
            "fontFamily": "Roboto"
          },
          "field": {
            "color": "rgba(0, 0, 0, 1)",
            "fontFamily": "Roboto",
            "borderColor": "rgba(208, 208, 208, 1)",
            "backgroundColor": "rgb(255, 255, 255)"
          },
          "image": {
            "backgroundColor": "rgba(0, 0, 0, 0)"
          },
          "wheel": {
            "color": "rgba(24, 23, 26, 1)",
            "fontFamily": "Roboto",
            "borderColor": "rgba(208, 208, 208, 1)",
            "backgroundColor": "rgb(255, 255, 255)",
            "secondaryBackgroundColor": "rgb(232, 232, 232)"
          },
          "button": {
            "color": "rgb(255, 255, 255)",
            "fontFamily": "Roboto",
            "borderColor": "rgba(0, 0, 0, 1)",
            "backgroundColor": "rgba(24, 23, 26, 1)"
          },
          "social": {
            "color": "rgba(0, 0, 0, 1)"
          },
          "spacer": {
            "color": "rgba(0, 0, 0, 0)"
          },
          "survey": {
            "color": "rgba(0, 0, 0, 1)",
            "fontFamily": "Roboto",
            "borderColor": "rgba(208, 208, 208, 1)",
            "backgroundColor": "rgb(232, 232, 232)"
          },
          "voucher": {
            "color": "rgba(0, 0, 0, 1)",
            "fontFamily": "Roboto",
            "borderColor": "rgba(208, 208, 208, 1)"
          },
          "checkbox": {
            "color": "rgba(24, 23, 26, 1)",
            "borderColor": "rgba(208, 208, 208, 1)",
            "checkboxColor": "rgba(24, 23, 26, 1)",
            "backgroundColor": "rgb(255, 255, 255)"
          },
          "headline": {
            "color": "rgba(24, 23, 26, 1)",
            "fontFamily": "Roboto"
          },
          "textarea": {
            "color": "rgba(0, 0, 0, 1)",
            "fontFamily": "Roboto",
            "borderColor": "rgba(208, 208, 208, 1)",
            "backgroundColor": "rgb(255, 255, 255)"
          },
          "countdown": {
            "color": "rgba(0, 0, 0, 1)",
            "fontFamily": "Roboto",
            "borderColor": "rgba(208, 208, 208, 1)"
          }
        },
        "active": true,
        "created_at": "2025-07-14T13:11:49.495162+03:00",
        "updated_at": "2025-08-06T14:21:18.991722+03:00"
      }
    };
  },
  methods: {
    setTargetImage(index) {
      if (index >= 0 && index < this.template.attributes.thumbnails.data.length) {
        this.targetImgIndex = index;
      }
    },
    changeTemplate(newIndex) {
      if (newIndex >= 0 && newIndex < this.totalTemplates) {
        this.$emit("template-change", newIndex);
      } else {
        console.warn(`Template index out of bounds: ${newIndex}`);
      }
    },
  }
};
</script>
