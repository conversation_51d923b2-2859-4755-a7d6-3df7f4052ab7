<template>
  <div class="flex flex-col items-start p-4 bg-white w-[300px]">
    <h3 class="text-xl font-bold mb-2">{{ slide.title }}</h3>
    <p class="text-sm text-gray-600 mb-2">{{
        slide.date ? $formatDate(slide.date) : "TBA"
      }}</p>
    <div class="text-gray-700 text-xs text-left" :class="$style.description" v-html="slide.description"></div>
  </div>
</template>

<script>
export default {
  name: "SwiperCarrouselSlideText",
  props: {
    slide: {
      type: Object,
      required: true,
    },
    photoSwipeActive: {
      type: Boolean,
      default: false,
    },
    fixedHeight: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style lang="scss" module>
.description {
  h4 {
    @apply mb-2 text-base;
  }

  p {
    @apply mb-2;
  }
}
</style>
