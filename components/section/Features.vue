<template>
  <section class="max-w-screen-lg py-16 sm:py-80 px-5 lg:px-0 mx-auto relative text-left">
    <h1 class="font-extrabold mb-16">We help you —</h1>

    <!-- Section 1 -->
    <div class="flex items-center justify-between flex-col mb-16 md:mb-80 sm:flex-row-reverse sm:w-[calc(100%-5rem)]">
      <div class="flex-1 w-full">
        <div class="w-full pb-[50%] relative mb-4">
          <img src="/images/water-creativity.png" alt="" class="absolute top-0 left-0 w-full h-full object-contain m-0">
        </div>
      </div>
      <div class="flex-1 text-left">
        <div>
          <span
            class="text-sm font-semibold uppercase mb-1 text-onvocado-secondary">All of our thoughts, in one place.</span>
        </div>
        <div>
          <h2 class="mb-4 text-2xl md:text-3xl font-semibold">Engage Your Audience</h2>
        </div>
        <div>
          <p>Work with your team together and realtime on any of your presentations. Organize your workflow with shared
            channels. Define a role for everyone and let them work in confidence.</p>
        </div>
      </div>
    </div>

    <!-- Section 2 -->
    <div class="flex items-center justify-between flex-col mb-16 md:mb-80 sm:flex-row sm:w-[calc(100%-5rem)]">
      <div class="flex-1 w-full">
        <div class="w-full pb-[50%] relative mb-4">
          <img src="/images/retain-customers.png" alt="" class="absolute top-0 left-0 w-full h-full object-contain m-0">
        </div>
      </div>
      <div class="flex-1 text-left">
        <div>
          <span class="font-semibold uppercase mb-1 text-onvocado-secondary">All of our thoughts, in one place.</span>
        </div>
        <div>
          <h2 class="mb-4 text-2xl md:text-3xl font-semibold">Retain Customers️</h2>
        </div>
        <div>
          <p>Create eye-catching forms that are matching with the look and feel of your website.</p>
        </div>
      </div>
    </div>

    <!-- Section 3 -->
    <div class="flex items-center justify-between flex-col mb-16 md:mb-80 sm:flex-row-reverse sm:w-[calc(100%-5rem)]">
      <div class="flex-1 w-full">
        <div class="w-full pb-[50%] relative mb-4">
          <img src="/images/water-creativity.png" alt="" class="absolute top-0 left-0 w-full h-full object-contain m-0">
        </div>
      </div>
      <div class="flex-1 text-left">
        <div>
          <span class="font-semibold uppercase mb-1 text-onvocado-secondary">All of our thoughts, in one place.</span>
        </div>
        <div>
          <h2 class="mb-4 text-2xl md:text-3xl font-semibold">Water your creativity ✍️</h2>
        </div>
        <div>
          <p>Convert website visitors into email subscribers while collecting additional information.</p>
        </div>
      </div>
    </div>

    <!-- Section 4 -->
    <div class="flex items-center justify-between flex-col mb-16 md:mb-80 sm:flex-row sm:w-[calc(100%-5rem)]">
      <div class="flex-1 w-full">
        <div class="w-full pb-[50%] relative mb-4">
          <img src="/images/data-analyze.png" alt="" class="absolute top-0 left-0 w-full h-full object-contain m-0">
        </div>
      </div>
      <div class="flex-1 text-left">
        <div>
          <span class="font-semibold uppercase mb-1 text-onvocado-secondary">All of our thoughts, in one place.</span>
        </div>
        <div>
          <h2 class="mb-4 text-2xl md:text-3xl font-semibold">Gain & Analyze Enhanced Data️</h2>
        </div>
        <div>
          <p>Convert website visitors into email subscribers while collecting additional information.</p>
        </div>
      </div>
    </div>

    <!-- Section 5 -->
<!--    <div class="flex items-center justify-between flex-col mb-8 sm:flex-row sm:w-[calc(100%-5rem)]">-->
<!--      <div class="flex-1 text-left">-->
<!--        <h1 class="text-4xl font-extrabold mb-16">Onvocado is made for <br class="hidden md:block">busy marketers</h1>-->
<!--        <p class="mb-8">Easily define your companions character using our user-friendly campaign builder.</p>-->
<!--        <div class="w-full pb-[50%] relative mb-16">-->
<!--          <img src="/images/visual-laptop.png" alt="" class="absolute top-0 left-0 w-full h-full object-contain m-0">-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->

  </section>
</template>

<script>
export default {
  name: "Features"
}
</script>
