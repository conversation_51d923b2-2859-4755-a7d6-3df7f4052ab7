<template>
  <div class="max-w-screen-2xl px-0 md:px-4 lg:px-0 mx-auto relative">
    <span class="container block mb-6 text-left text-xl md:text-lg font-normal text-onvocado-gray-darker"
          v-html="$t('clients.trustedBy')">
    </span>
    <div
      id="logoCarousel"
      class="w-full inline-flex flex-nowrap overflow-hidden [mask-image:_linear-gradient(to_right,transparent_0,_black_128px,_black_calc(100%-128px),transparent_100%)]">
      <ul ref="logos"
          class="flex items-center justify-center md:justify-start [&_li]:mx-4 [&_li]:md:mx-8 [&_img]:max-w-28 [&_img]:opacity-40">
        <li>
          <nuxt-img src="/images/logos/eddy_logo.svg" :alt="$t('clients.alt.eddy')" width="100" height="50" lazy/>
        </li>
        <li>
          <nuxt-img src="/images/logos/frank_logo.svg" :alt="$t('clients.alt.frank')" width="100" height="50" lazy/>
        </li>
        <li>
          <nuxt-img class="w-[42px]" src="/images/logos/lusio_logo.png" :alt="$t('clients.alt.lusio')" width="76"
                    height="116" lazy/>
        </li>
        <li>
          <nuxt-img src="/images/logos/shares_logo.svg" :alt="$t('clients.alt.shares')" width="128" height="32" lazy/>
        </li>
        <li>
          <nuxt-img class="w-[82px]" src="/images/logos/WWF_logo.svg" :alt="$t('clients.alt.wwf')" width="140"
                    height="157" lazy/>
        </li>
        <li>
          <nuxt-img class="onv-grayscale" src="/images/logos/kingservers.webp" :alt="$t('clients.alt.kingservers')"
                    width="90" height="50" lazy/>
        </li>
        <li>
          <nuxt-img src="/images/logos/logo3.svg" :alt="$t('clients.alt.client3')" width="100" height="50" lazy/>
        </li>
        <li>
          <nuxt-img src="/images/logos/logo4.svg" :alt="$t('clients.alt.client4')" width="100" height="50" lazy/>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: "Clients",
  mounted() {
    let ul = this.$refs.logos;
    ul.insertAdjacentHTML('afterend', ul.outerHTML);
    ul.nextSibling.setAttribute('aria-hidden', 'true');
    ul.classList.add('animate-infinite-scroll');
    ul.nextSibling.classList.add('animate-infinite-scroll');
  }
}
</script>

<style lang="scss" scoped>
.onv-grayscale {
  filter: grayscale(1);
}
</style>
