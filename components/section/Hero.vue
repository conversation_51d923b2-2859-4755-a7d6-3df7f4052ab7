<template>
  <section
    class="
      xl:min-h-[calc(100vh-120px]
      flex
      flex-col
      md:flex-col
      justify-end overflow-hidden relative">

    <div class="container max-w-screen-xl">
      <div class="mt-4 md:mt-0 mb-6 md:mb-16 lg:mb-24 md:flex md:flex-row">
        <div class="w-full">
          <h1 class="mb-8 md:mt-20 md:mb-10 py-0">
            {{ $t('hero.title') }}
          </h1>
          <div style="transition-delay: 500ms">
            <p class="mb-5 md:mb-10 max-w-[530px] text-xl md:text-lg">
              {{ $t('hero.description') }}
            </p>

            <div class="flex">
              <a href="https://app.onvocado.com/register" class="button big">{{ $t('hero.start_free') }}</a>
            </div>
          </div>
        </div>
        <div class="w-full mt-12 md:mt-16 mb-6 md:mb-0 md:ml-0 ">

          <h2 class="mb-2 text-center md:hidden">{{ $t('hero.templates_title') }}</h2>

          <p class="mb-6 text-xl text-center md:hidden">{{ $t('hero.templates_subtitle') }}</p>

          <ResponsivePicture v-if="imageAttributes !== null"
                             class="!h-fit mb-8 md:mb-0"
                             :imgClass="'w-full'"
                             :isImgFullscreen="false"
                             :alt="alternativeText"
                             :caption="caption"
                             :smallURL="getImageOfSize(imageAttributes, 'small')"
                             :mediumURL="getImageOfSize(imageAttributes, 'medium')"
                             :largeURL="getImageOfSize(imageAttributes, 'large')"
                             :xlargeURL="getImageOfSize(imageAttributes, 'xlarge')"/>

          <div class="flex md:hidden justify-center">
            <ul class="space-y-4 text-left">
              <li class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 300" class="w-3 mr-4 rotate-90" aria-hidden="true">
                  <path fill="#3f5344" d="M194.58 167.48a93.225 93.225 0 0 0-3.96-9.82L137.88 27.12C132.52 11.34 117.58 0 100 0S68.98 10.24 63 24.8h-.02L7.86 161.08l-.36.88C2.66 173.68 0 186.52 0 200c0 55.22 44.78 100 100 100s100-44.78 100-100c0-11.38-1.9-22.32-5.42-32.52ZM100 260c-33.14 0-60-26.86-60-60s26.86-60 60-60 60 26.86 60 60-26.86 60-60 60Z"/>
                </svg>
                <span class="text-xl font-semibold">{{ $t('hero.feature_1') }}</span>
              </li>
              <li class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 300" class="w-3 mr-4 rotate-90" aria-hidden="true">
                  <path fill="#3f5344" d="M194.58 167.48a93.225 93.225 0 0 0-3.96-9.82L137.88 27.12C132.52 11.34 117.58 0 100 0S68.98 10.24 63 24.8h-.02L7.86 161.08l-.36.88C2.66 173.68 0 186.52 0 200c0 55.22 44.78 100 100 100s100-44.78 100-100c0-11.38-1.9-22.32-5.42-32.52ZM100 260c-33.14 0-60-26.86-60-60s26.86-60 60-60 60 26.86 60 60-26.86 60-60 60Z"/>
                </svg>
                <span class="text-xl font-semibold">{{ $t('hero.feature_2') }}</span>
              </li>
              <li class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 300" class="w-3 mr-4 rotate-90" aria-hidden="true">
                  <path fill="#3f5344" d="M194.58 167.48a93.225 93.225 0 0 0-3.96-9.82L137.88 27.12C132.52 11.34 117.58 0 100 0S68.98 10.24 63 24.8h-.02L7.86 161.08l-.36.88C2.66 173.68 0 186.52 0 200c0 55.22 44.78 100 100 100s100-44.78 100-100c0-11.38-1.9-22.32-5.42-32.52ZM100 260c-33.14 0-60-26.86-60-60s26.86-60 60-60 60 26.86 60 60-26.86 60-60 60Z"/>
                </svg>
                <span class="text-xl font-semibold">{{ $t('hero.feature_3') }}</span>
              </li>
            </ul>
          </div>

        </div>
      </div>
    </div>

    <SectionClients class="container mt-8 md:my-12 overflow-hidden"/>
  </section>
</template>


<script>
import ImageMeta from "@/mixins/imageMeta.js";

export default {
  name: "Hero",
  mixins: [ImageMeta],
  props: {
    heroData: Object,
  },
  computed: {
    imageAttributes() {
      return this.getAttribute(this.heroData, 'image.data.attributes');
    },
    supportingImageAttributes() {
      return this.getAttribute(this.heroData, 'supportingImage.data.attributes');
    },
    supportingImageAlternativeText() {
      return (this.supportingImageAttributes && this.supportingImageAttributes.alternativeText) ? this.supportingImageAttributes.alternativeText : '';
    },
  }
}
</script>
