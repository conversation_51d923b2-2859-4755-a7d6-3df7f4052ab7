<template>
  <div class="section-wrapper px-0 mx-auto">
    <client-only>
      <div class="section-wrapper px-0 mx-auto xl:max-w-4xl container">
        <h2 v-if="data.title" v-html="data.title" class="mb-4 md:mb-8"></h2>
      </div>
      <!-- Use isText to force text slides -->
      <SwiperCarrousel
        :slides="formattedSlides"
        :photoSwipeActive="false"
        :isText="true"
        :customOptions="customSwiperOptions"
        :showNavigation="true"
        :startAtEnd="true" />
    </client-only>
  </div>
</template>

<script>
import SwiperCarrousel from "@/components/SwiperCarrousel.vue";

export default {
  name: "RoadmapSlider",
  components: {
    SwiperCarrousel
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    formattedSlides() {
      if (!this.data.items) return [];
      return this.data.items.map(item => ({
        title: item.title,
        date: item.date,
        description: item.description
      }));
    },
    customSwiperOptions() {
      return {
        loop: false,
        autoplay: false,
        loopFillGroupWithBlank: false,
        initialSlide: this.data?.items?.length > 0 ? this.data.items.length - 1 : 0
      };
    }
  }
};
</script>
