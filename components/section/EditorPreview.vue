<template>
  <div class="md:container !max-w-screen-xl py-16 mx-auto">
    <div class="py-10 md:py-12 md:px-16 2xl:px-20 bg-body-gray-dark rounded-lg md:rounded-3xl">

      <div class="container mb-8 md:mb-12">
        <h2 class="mb-2 md:mb-4 text-center md:text-5xl">{{ $t('editorPreview.title') }}</h2>
        <p class="mb-4 text-xl text-center">{{ $t('editorPreview.description') }}</p>
      </div>

      <div class="swiper" ref="swiperElement" id="swiper-gallery">
        <div class="swiper-wrapper">
          <div class="swiper-slide !flex justify-center" v-for="(slide, index) in slides" :key="index">
            <nuxt-picture :src="slide.imgUrl"
                          :alt="$t(slide.altKey)"
                          class="rounded-lg md:rounded-3xl"
                          width="3300"
                          height="2160"
                          loading="lazy"
                          sizes="xl:140vw lg:120vw md:100vw sm:100vw xs:100vw"/>
          </div>
        </div>
      </div>

      <ol
        class="w-full p-3 sm:p-4 m-auto hidden md:flex justify-center items-center space-x-2 sm:space-x-4 font-medium text-center text-onvocado-gray-dark overflow-hidden">
        <li v-for="(slide, index) in slides" :key="index"
            class="flex items-center cursor-pointer"
            :class="index === activeIndex ? 'text-body-black' : ''"
            @click="setActiveSlide(index)">
          <span :class="index === activeIndex ? 'text-onvocado-primary-dark' : 'text-body-black'">
            {{ $t(slide.nameKey) }}
          </span>
          <svg v-if="index !== (slides.length - 1)" class="w-3 h-3 ml-2 sm:ml-4 rtl:rotate-180 text-onvocado-gray"
               aria-hidden="true"
               xmlns="http://www.w3.org/2000/svg"
               fill="none" viewBox="0 0 12 10">
            <path stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="m7 9 4-4-4-4M1 9l4-4-4-4"/>
          </svg>
        </li>
      </ol>
    </div>
  </div>
</template>

<script>
export default {
  name: "EditorPreview",
  data() {
    return {
      slides: [
        {
          nameKey: "editorPreview.slide_1_name",
          imgUrl: "/images/templates.webp",
          altKey: "editorPreview.slide_1_alt"
        },
        {
          nameKey: "editorPreview.slide_2_name",
          imgUrl: "/images/editor.webp",
          altKey: "editorPreview.slide_2_alt"
        },
        {
          nameKey: "editorPreview.slide_3_name",
          imgUrl: "/images/dashboard.webp",
          altKey: "editorPreview.slide_3_alt"
        },
        {
          nameKey: "editorPreview.slide_4_name",
          imgUrl: "/images/analytics.webp",
          altKey: "editorPreview.slide_4_alt"
        }
      ],
      activeIndex: 0,
      swiper: null,
      windowWidth: 0,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    swiperOptions() {
      return {
        ...(this.isMobile ? {} : {
          effect: 'fade',
          speed: 1000,
          fadeEffect: {
            crossFade: true
          }
        }),
        autoplay: {
          delay: 3000,
          disableOnInteraction: false,
        },
        spaceBetween: 10,
        slidesPerView: 1.15,
        on: {
          slideChange: () => {
            this.activeIndex = this.swiper.realIndex;
          },
        },
        breakpoints: {
          768: {
            centeredSlides: false,
            spaceBetween: 0,
            slidesPerView: 1,
          },
        }
      };
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initializeSwiper();
    });
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    handleResize() {
      if (this.swiper) {
        this.swiper.destroy(true, true);
        this.initializeSwiper();
      }
    },
    initializeSwiper() {
      this.swiper = new this.$swiper(this.$refs.swiperElement, {
        modules: [this.$swiperModules.EffectFade, this.$swiperModules.Autoplay],
        ...this.swiperOptions,
      });
    },
    setActiveSlide(index) {
      this.swiper.slideTo(index);
      this.activeIndex = index;
    },
  }
}
</script>
