<template>
  <section
    class="section-wrapper w-full flex items-center bg-body-gray">
     <div class="mx-auto max-w-2xl flex flex-col justify-center">

      <div class="m-auto mb-2 flex gap-x-4 text-xl">
        <span :class="{ 'font-semibold': billingPeriod === 'monthly' }">{{ $t('Monthly') }}</span>
        <el-switch
            v-model="billingPeriod"
            :width="46"
            active-color="#C8E85E"
            inactive-color="#C8E85E"
            active-value="yearly"
            inactive-value="monthly"></el-switch>
        <span :class="{ 'font-semibold': billingPeriod === 'yearly' }">{{ $t('Annually') }}</span>
      </div>
      <span :class="{ 'translate-y-6 font-bold scale-150': billingPeriod === 'yearly' }" class="m-auto text-sm transition-all">{{ $t('Save with annual plans') }}</span>
    </div>
    <Plans :available-plans="availablePlansPerCurrency"
           :billing-period="billingPeriod"
           :currency="currency"
           v-on="$listeners"/>
  </section>
</template>

<script>
export default {
  name: "PricingPlans",
  props: {
    availablePlans: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      billingPeriod: 'monthly',
      currency: 'eur',
    };
  },
  computed: {
    availablePlansPerCurrency() {
      return this.availablePlans.filter((plan) => {
        return (
          (plan.currency.toLowerCase().includes(this.currency) &&
            plan.billingPeriod.toLowerCase().includes(this.billingPeriod)) ||
          plan.id === 'basic'
        )
      })
    },
  },
}
</script>
