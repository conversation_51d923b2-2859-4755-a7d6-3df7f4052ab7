<template>
  <section class="py-6 md:pb-36 md:pt-10 bg-body-gray-dark">
    <h2 class="px-4 lg:px-20 mb-10 hidden md:block text-5xl text-left">Similar templates</h2>
    <div class="px-5 lg:px-0 flex" v-if="relatedPosts.length > 0">
      <div v-for="(post, index) in relatedPosts"
           :key="post.id"
           class="w-full md:w-1/2"
           :class="index > 0 ? 'hidden md:flex' : 'flex flex-col md:flex-row'">
        <span class="pl-5 mt-2 mb-4 text-lg font-bold md:hidden"
              @mouseover="mouseEvent( true, post.id)"
              @mouseleave="mouseEvent(false, post.id)">Next template</span>
        <NuxtLink
          :to="`${$i18n.locale === $i18n.defaultLocale ? '' : '/' + $i18n.locale}${i18nPages['templates/_slug'][$i18n.locale].replace(':slug', post.attributes?.slug)}`"
          :aria-label="$t('Related post')"
          class="md:w-1/2 lg:pl-20 mb-2 md:mb-0 grow z-20">
          <div class="border-4 border-body-black rounded-3xl overflow-hidden"
               @mouseover="mouseEvent(true, post.id)"
               @mouseleave="mouseEvent( false, post.id)">
            <BlurHashResponsivePicture v-if="post.attributes.thumbnails.data[0]"
                                       class="w-full aspect-4/3 overflow-hidden"
                                       :img-class="'!object-contain related-' + post.id"
                                       :image="post.attributes.thumbnails.data[0]"/>
          </div>

        </NuxtLink>
        <div class="md:w-1/2 pl-5 md:pl-8 flex flex-col-reverse md:flex-col justify-center"
             :class="index > 0 ? 'lg:pr-12 2xl:pr-20' : ''">
          <NuxtLink
            :to="`${$i18n.locale === $i18n.defaultLocale ? '' : '/' + $i18n.locale}${i18nPages['templates/_slug'][$i18n.locale].replace(':slug', post.attributes?.slug)}`"
            :aria-label="$t('Related post')">
            <h3 v-if="post.attributes.title" class="mb-2 md:text-2xl line-clamp-3 lg:line-clamp-4 2xl:lg:line-clamp-5">
              {{ post.attributes.title }}
            </h3>
            <span class="hidden md:block"
                  @mouseover="mouseEvent( true, post.id)"
                  @mouseleave="mouseEvent(false, post.id)">{{ $t('Read more') }}</span>
          </NuxtLink>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import ImageMeta from "@/mixins/imageMeta.js";
import {i18nPages} from "@/i18n.config.js";

export default {
  name: "RelatedTemplates",
  mixins: [ImageMeta],
  props: {
    posts: Array,
    currentPostID: Number,
  },
  data() {
    return {
      i18nPages,
    };
  },
  computed: {
    relatedPosts() {
      const currentIndex = this.posts.findIndex(
        (post) => post.id === this.currentPostID
      );

      // If array has 1 or 2 elements
      if (this.posts.length === 1) {
        return [];
      } else if (this.posts.length === 2) {
        if (currentIndex === 0) {
          return [this.posts[1]];
        } else {
          return [this.posts[0]];
        }
      }

      let startIndex = currentIndex + 1;
      let endIndex = currentIndex + 2;

      // If array has more than 2 elements
      if (currentIndex === this.posts.length - 1) {
        startIndex = 0;
        endIndex = 1;
      } else if (currentIndex === this.posts.length - 2) {
        endIndex = 0;
      }

      return [this.posts[startIndex], this.posts[endIndex]];
    },
  },
  methods: {
    mouseEvent(state, postID) {
      const image = document.getElementsByClassName('related-' + postID)[0]

      this.$anime.remove(image);

      if (state) {
        this.$anime({
          targets: image,
          scale: 1.1,
          duration: 2000,
          easing: 'cubicBezier(0.2, 1, 0.36, 1)'
        });
      } else {
        this.$anime({
          targets: image,
          scale: 1,
          duration: 700,
          easing: 'easeOutExpo'
        });
      }
    },
  }
}
</script>
