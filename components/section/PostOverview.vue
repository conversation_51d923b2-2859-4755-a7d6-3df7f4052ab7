<template>
  <section class="px-4 lg:px-0 mb-20 md:mb-32">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 md:gap-x-12 gap-y-8 md:gap-y-16">
      <article
        v-for="(post) in posts"
        :key="post.id"
        :class="{'md:col-span-2': post.attributes?.featured}"
        class="last-of-type:mb-11">
          <PostOverviewItem :post="post"/>
      </article>
    </div>
  </section>
</template>

<script>
export default {
  name: "PostOverview",
  props: {
    posts: Array,
  },
}
</script>
