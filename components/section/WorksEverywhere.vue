<template>
  <section :class="$style.worksEverywhere" class="m-auto">
    <div :class="$style.details">
      <h2 class="mb-4 text-3xl md:text-6xl font-extrabold">Works on any platform</h2>
      <p class="sm:text-lg mb-8">Easy to install on all web platforms. <br>Just
        place your custom embed code and you have
        it!
        <br>Can also be deployed using Google Tag Manager.</p>
      <button
        class="button big"
        @click.prevent="scrollTo('top')">
        Try Onvocado for free
      </button>
    </div>

    <div :class="$style.bundle">
      <div>
        <nuxt-img
          src="/images/logos/wordpress_monotone_black.svg"
          alt="Logo of WordPress"
          width="150"
          height="150"
          lazy
        />
      </div>
      <div>
        <nuxt-img
          src="/images/logos/magento_monotone_black.svg"
          alt="Logo of Magento"
          width="150"
          height="150"
          lazy
        />
      </div>
      <div>
        <nuxt-img
          src="/images/logos/squarespace_monotone_black.svg"
          alt="Logo of Squarespace"
          width="150"
          height="150"
          lazy
        />
      </div>
      <div>
        <nuxt-img
          src="/images/logos/woocomerce_monotone_black.svg"
          alt="Logo of WooCommerce"
          width="150"
          height="150"
          lazy
        />
      </div>
      <div>
        <nuxt-img
          src="/images/logos/magento_monotone_black.svg"
          alt="Logo of Magento"
          width="150"
          height="150"
          lazy
        />
      </div>
      <div>
        <nuxt-img
          src="/images/logos/shopify_monotone_black.svg"
          alt="Logo of Shopify"
          width="150"
          height="150"
          lazy
        />
      </div>
    </div>
    </div>
  </section>
</template>

<script>
export default {
  name: "WorksEverywhere",
  methods: {
    scrollTo(position) {
      this.$store.dispatch('triggerScrollTo', position);
    },
  }
}
</script>

<style lang="scss" module>
.worksEverywhere {
  max-width: 1140px;
  padding: 180px 20px 120px 20px;
  position: relative;
  text-align: center;
  overflow: visible;
  display: flex;
  flex-direction: column;
}

.details {
  margin: 0 auto 80px auto;
  text-align: left;
}

$opacity: 0.09;

.bundle {
  position: relative;
  height: 80vw;
  width: 100%;
  margin: 40px auto auto auto;

  img {
    width: 65%;
    height: 40%;
  }

  div {
    width: 32vw;
    height: 32vw;
    position: absolute;
    border-radius: 50%;
    //box-shadow: 1px 8px 20px 0 rgba(#e7edd1, .25);
    display: flex;
    align-items: center;
    justify-content: center;

    &:nth-of-type(1) {
      top: 20vw;
      left: auto;
      right: 4vw;
      transform: scale(0.85);
      //background-color: rgba(#e7edd1, $opacity);
    }

    &:nth-of-type(2) {
      top: 21vw;
      left: 26vw;
      transform: scale(0.40);
    }

    &:nth-of-type(3) {
      top: 40vw;
      left: 0;
      transform: scale(0.97);
      //background-color: rgba(#e7edd1, $opacity);
    }

    &:nth-of-type(4) {
      top: 51vw;
      left: 37vw;
      transform: scale(0.67);
      //background-color: rgba(#e7edd1, $opacity);
    }

    &:nth-of-type(5) {
      top: 0;
      left: 12vw;
      transform: scale(0.77);
      //background-color: rgba(#e7edd1, $opacity);
    }

    &:nth-of-type(6) {
      top: -30px;
      left: 44vw;
      transform: scale(0.61);
    }
  }
}

@media screen and (min-width: 768px) {
  .worksEverywhere {
    padding: 260px 20px 120px 20px;
  }

  .details {
    margin: 0;
  }
}

@media screen and (min-width: 1140px) {
  .worksEverywhere {
    padding: 260px 0 120px 0;
    text-align: left;
    flex-direction: row;
  }

  .bundle {
    width: 50%;
    height: 37vw;
    margin: auto;

    div {
      width: 14vw;
      height: 14vw;

      &:nth-of-type(1) {
        top: 18vw;
        left: 27vw;
        right: auto;
      }

      &:nth-of-type(2) {
        top: 11vw;
        left: 16vw;
      }

      &:nth-of-type(3) {
        top: 10vw;
        left: -3vw;
        transform: scale(0.55);
      }

      &:nth-of-type(4) {
        top: 19vw;
        left: 8vw;
      }

      &:nth-of-type(5) {
        top: 0;
        left: 12vw;
      }

      &:nth-of-type(6) {
        top: -12px;
        left: 28vw;
      }
    }
  }
}


@media screen and (min-width: 1900px) {
  .bundle {
    div {
      width: 12vw;
      height: 12vw;
    }
  }
}
</style>
