<template>
  <section class="px-4 lg:px-0 mb-20 md:mb-32">
    <h2 class="text-3xl font-bold mb-8">{{ title }}</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-x-8 md:gap-x-12 gap-y-8 md:gap-y-16">
      <article
        v-for="(item) in supportCategoryItems"
        :key="item.id">
        <SupportCategoryOverviewItem :item="item"/>
      </article>
    </div>
  </section>
</template>


<script>
import SupportOverviewItem from "@/components/SupportOverviewItem.vue";
import IntegrationOverviewItem from "@/components/IntegrationOverviewItem.vue";

export default {
  name: "SupportOverview",
  components: {
    SupportOverviewItem,
    IntegrationOverviewItem,
  },
  props: {
    title: {
      type: String,
      required: false,
    },
    supportCategoryItems: {
      type: Array,
      required: true,
    },
  },
};
</script>

