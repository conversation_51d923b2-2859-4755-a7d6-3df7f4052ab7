<template>
  <div id="mc_embed_signup" :class="[{ 'is-outline': outline }, 'max-w-2xl']">
    <form id="mc-embedded-subscribe-form"
          action="https://onvocado.us20.list-manage.com/subscribe/post?u=7ac90352d4e0ee2ee088ef51f&amp;id=fdc41e4419"
          method="post" target="_blank">
      <div class="field is-grouped">
        <div id="mc_embed_signup_scroll" class="flex">
          <button id="mc-embedded-subscribe" name="subscribe" type="submit"
                  :value="btnText"
                  :class="['button dark-bg big flex-none']">{{ btnText }}
          </button>
          <input id="mce-EMAIL" name="EMAIL" required="required" type="email"
                 value="" :placeholder="$t('E-mail address')" class="email input is-medium w-full grow !placeholder-body-gray-darker">
          <div aria-hidden="true" style="position: absolute; left: -5000px;"><input
            tabindex="-1"
            name="b_207993bdb967b8535b71971a0_f34c14055d"
            type="text" value=""></div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
export default {
  name: 'MailChimpInput',
  props: {
    btnText: {
      type: String,
      required: false,
      default: "Get Early Access"
    },
    outline: {
      type: Boolean,
      required: false,
      default: false
    }
  }
}
</script>

<style lang="scss">
#mc-embedded-subscribe {
  position: relative;
  z-index: 5;
}

#mc_embed_signup_scroll {
  border-radius: 24px 0 0 0;
  background-color: white;

  button {
    border-radius: 24px 0 24px 0;
  }
}

#mc_embed_signup.is-outline {
  #mc_embed_signup_scroll {
    background-color: transparent;
    box-shadow: inset 0 0 0 1px #FFF;
    border-radius: 24px;

    input {
      color: #FFF;

      &::placeholder {
        color: #abb1b9;
      }
    }
  }
}

#mce-EMAIL {
  padding: 13px 36px;
  margin-left: -24px;
  position: relative;
  vertical-align: top;
  background-color: transparent;
  color: black;
  font-size: 16px;
  border: none;
  box-shadow: none;
  outline: none;
  z-index: 0;
}

@media screen and (max-width: 768px) {
  #mc_embed_signup_scroll {
    display: flex;
    flex-direction: column;
    box-shadow: inset 0 0 0 2px #f6f7f6 !important;
  }

  #mc-embedded-subscribe {
    order: 2;
  }

  #mce-EMAIL {
    width: 89%;
    margin: auto;
  }
}
</style>
