<template>
  <NuxtLink :to="$localePathWithTrailingSlash(`/blog/${post.attributes?.slug}/`)"
            :aria-label="$t('View blog post')"
            class="link"
            ref="link"
            :class="[$style.link, isActive ? $style.activeLink : '']">
    <template v-if="post.attributes?.featured">
      <div class="md:flex justify-between items-start">
        <div class="md:mb-auto overflow-hidden transition-all duration-500">
          <BlurHashResponsivePicture v-if="post.attributes?.thumbnail?.data" class="w-full aspect-16/9" :image="post.attributes?.thumbnail?.data"/>
        </div>
        <div class="lg:w-2/3 md:ml-12">
          <div class="mt-1 mb-4 flex text-onvocado-secondary items-end">
            <span class="mr-1 text-sm font-semibold uppercase tracking-wide">Featured</span>
            <FireIcon class="w-6"/>
          </div>
          <h2 class="mb-3 md:mb-6 text-2xl md:text-3xl lg:text-4xl line-clamp-5">
            {{ post.attributes?.title }}
          </h2>
          <p class="mb-4 text-onvocado-gray-dark line-clamp-5">
            {{ post.attributes?.subtitle }}
          </p>
          <p class="text-sm text-onvocado-gray">
            {{ $formatDate(post.attributes?.updatedAt) }}
          </p>
        </div>
      </div>
    </template>

    <template v-else>
      <div class="flex flex-col">
        <div class="mb-4 overflow-hidden transition-all duration-500">
          <BlurHashResponsivePicture v-if="post.attributes?.thumbnail?.data" class="aspect-16/9" :image="post.attributes?.thumbnail?.data"/>
        </div>

        <div>
          <h2 v-if="post.attributes?.title" class="mb-3 block text-2xl">{{ post.attributes?.title }}</h2>
          <p class="mb-2 text-onvocado-gray-dark">
            {{ post.attributes?.subtitle }}
          </p>
          <span v-if="post.attributes?.publishedDate"
                class="inline-block text-sm text-onvocado-gray capitalize">
            {{ $formatDate(post.attributes?.publishedDate) }}</span>
        </div>
      </div>
    </template>
  </NuxtLink>
</template>

<script>
import ImageMeta from "@/mixins/imageMeta.js";
import FireIcon from "~/static/icons/fire.svg?inline";

export default {
  name: "PostOverviewItem",
  props: {
    post: Object,
  },
  mixins: [ImageMeta],
  components: {FireIcon},
  computed: {
    thumbnailAttributes() {
      return this.getAttribute(this.post, 'attributes.thumbnail.data.attributes');
    }
  },
  data() {
    return {
      isActive: false
    };
  },
  mounted() {
    this.initAnimation();
  },
  methods: {
    initAnimation() {
      if (this.$refs.link && this.$refs.link.$el) {
        this.$refs.link.$el.addEventListener('mouseenter', this.zoomIn);
        this.$refs.link.$el.addEventListener('touchstart', this.zoomIn);
        this.$refs.link.$el.addEventListener('mouseleave', this.zoomOut);
        this.$refs.link.$el.addEventListener('touchend', this.zoomOut);
      }
    },
    zoomIn() {
      const image = this.$refs.link.$el.querySelector('img');

      this.isActive = true;
      this.$anime.remove(image);
      this.$anime({
        targets: image,
        scale: 1.1,
        duration: 2000,
        easing: 'cubicBezier(0.2, 1, 0.36, 1)'
      });
    },
    zoomOut() {
      if (!this.$refs.link) return;
      const image = this.$refs.link.$el.querySelector('img');

      this.isActive = false;
      this.$anime.remove(image);
      this.$anime({
        targets: image,
        scale: 1,
        duration: 700,
        easing: 'easeOutExpo'
      });
    }
  },
}
</script>

<style lang="scss" module>
.link {
  &:hover {
    > div:first-child {
      @apply lg:rounded-[40px];
    }

    button {
      @apply lg:opacity-100;
    }
  }
}

.activeLink {
  > div:first-child {
    > div:first-child {
      @apply rounded-[40px];
    }
  }

  button {
    @apply opacity-100;
  }
}
</style>
