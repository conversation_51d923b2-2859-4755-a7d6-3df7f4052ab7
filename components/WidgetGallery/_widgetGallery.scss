.widgetDetailsContainer {
  @apply h-full rounded-xl relative flex flex-col items-center justify-start;
  min-height: 350px;

  &:hover {
    .templateActionButtons {
      @apply opacity-100;
    }
  }
}

.widgetThumbnail {
  border-color: rgba(174, 178, 158, .5);
  aspect-ratio: 16 / 10;

  &:hover {
    border-color: rgba(15, 40, 21, .8);
  }
}

.tags {
  li {
    &:first-of-type {
      &::before {
        display: none;
      }
    }

    &::before {
      content: "•";
      display: inline;
      margin-right: 8px;
    }
  }
}
