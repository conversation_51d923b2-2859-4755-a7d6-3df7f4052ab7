import WidgetStepPreview from '@/libs/widget-step-preview.common.js'
import '@/libs/widget-step-preview.css'

export default {
  name: "WidgetGallery",
  components: {
    WidgetStepPreview,
  },
  props: {
    templates: {
      type: Object,
      required: true,
    },
    hidePreview: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      // ToDo retrieve templates per widgetPreview in view from /v1/api/widget-templates/{id}/
      // Here's a demo call:
      fakeTemplate: {
        "id": 2,
        "name": "Sign Up bar",
        "type": "stickyBar",
        "tag": "sticky bar",
        "categories": [
          "lead-generation",
          "coupons-offers"
        ],
        "industries": [],
        "template_theme": "default",
        "description": "A sleek sticky bar designed to drive lead generation by encouraging users to sign up for exclusive coupons and offers.",
        "priority": 10,
        "steps": [
          {
            "id": "1p37o3m",
            "contentItems": [
              {
                "id": "bys5iof",
                "type": "headline",
                "column": "A",
                "content": {
                  "text": "SIGN UP FOR 20% OFF!"
                },
                "styling": {
                  "color": "rgba(254, 254, 254, 1)",
                  "fontSize": "48px",
                  "textAlign": "left",
                  "fontFamily": "Ubuntu Mono",
                  "fontWeight": "bold"
                },
                "category": "basic",
                "settings": {
                  "usesThemeStyles": true
                }
              },
              {
                "id": "p3nsf5x",
                "type": "headline",
                "column": "B",
                "content": {
                  "text": "I am a headline"
                },
                "styling": {
                  "color": "rgba(24, 23, 26, 1)",
                  "fontSize": "22px",
                  "textAlign": "center",
                  "fontFamily": "Roboto",
                  "fontWeight": "normal"
                },
                "category": "basic",
                "settings": {
                  "usesThemeStyles": true
                }
              },
              {
                "id": "4j9iuom",
                "type": "field",
                "column": "B",
                "content": {
                  "placeholder": "Email"
                },
                "styling": {
                  "color": "rgba(13, 13, 13, 1)",
                  "fontSize": "20px",
                  "textAlign": "left",
                  "fontFamily": "Lato",
                  "fontWeight": "400",
                  "borderColor": "rgba(0, 0, 0, 0.3)",
                  "borderWidth": "0px",
                  "borderRadius": "8px",
                  "backgroundColor": "rgba(254, 254, 254, 1)"
                },
                "category": "form",
                "settings": {
                  "type": "email",
                  "label": "Email address",
                  "unique": false,
                  "required": true,
                  "usesThemeStyles": true
                }
              },
              {
                "id": "pp2svgg",
                "type": "button",
                "column": "B",
                "content": {
                  "text": "GET DISCOUNT"
                },
                "styling": {
                  "color": "rgba(242, 248, 248, 1)",
                  "fontSize": "18px",
                  "fontFamily": "Lato",
                  "fontWeight": "bold",
                  "borderColor": "rgba(242, 248, 248, 1)",
                  "borderWidth": "0",
                  "borderRadius": "2px",
                  "justifyContent": "center",
                  "backgroundColor": "rgba(20, 20, 20, 0)"
                },
                "category": "basic",
                "settings": {
                  "href": "",
                  "action": "next",
                  "backBtn": false,
                  "usesThemeStyles": true
                }
              }
            ]
          },
          {
            "id": "xop4ymt",
            "contentItems": [
              {
                "id": "giyh3je",
                "type": "headline",
                "column": "A",
                "content": {
                  "text": "ENJOY YOUR 50% OFF"
                },
                "styling": {
                  "color": "rgba(254, 254, 254, 1)",
                  "fontSize": "48px",
                  "textAlign": "center",
                  "fontFamily": "Ubuntu Mono",
                  "fontWeight": "bold"
                },
                "category": "basic",
                "settings": {
                  "usesThemeStyles": true
                }
              },
              {
                "id": "3r8w12c",
                "type": "spacer",
                "column": "A",
                "content": {},
                "styling": {
                  "color": "rgba(0, 0, 0, 0)",
                  "padding": "10px"
                },
                "category": "other",
                "settings": {
                  "usesThemeStyles": true
                }
              },
              {
                "id": "uxtlici",
                "type": "voucher",
                "column": "A",
                "content": {
                  "text": "VOUCHER CODE"
                },
                "styling": {
                  "color": "rgba(254, 254, 254, 1)",
                  "fontSize": "36px",
                  "textAlign": "center",
                  "fontFamily": "Ubuntu Mono",
                  "fontWeight": "bold",
                  "borderColor": "rgba(254, 254, 254, 1)",
                  "borderWidth": "5px",
                  "borderRadius": "8px"
                },
                "category": "basic",
                "settings": {
                  "label": "Voucher",
                  "usesThemeStyles": true
                }
              },
              {
                "id": "505bm5u",
                "type": "text",
                "column": "A",
                "content": {
                  "text": "An email with the best deals and savings is coming soon!"
                },
                "styling": {
                  "color": "rgba(254, 254, 254, 1)",
                  "fontSize": "20px",
                  "textAlign": "center",
                  "fontFamily": "Lato",
                  "fontWeight": "400"
                },
                "category": "basic",
                "settings": {
                  "usesThemeStyles": true
                }
              },
              {
                "id": "33pwfwb",
                "type": "spacer",
                "column": "A",
                "content": {},
                "styling": {
                  "color": "rgba(0, 0, 0, 0)",
                  "padding": "14px"
                },
                "category": "other",
                "settings": {
                  "usesThemeStyles": true
                }
              },
              {
                "id": "48nrxyv",
                "type": "button",
                "column": "A",
                "content": {
                  "text": "CONTINUE SHOPPING"
                },
                "styling": {
                  "color": "rgba(242, 248, 248, 1)",
                  "fontSize": "18px",
                  "fontFamily": "Lato",
                  "fontWeight": "bold",
                  "borderColor": "rgba(242, 248, 248, 1)",
                  "borderWidth": "0",
                  "borderRadius": "2px",
                  "justifyContent": "center",
                  "backgroundColor": "rgba(0, 0, 0, 0)"
                },
                "category": "basic",
                "settings": {
                  "href": "",
                  "action": "close",
                  "backBtn": false,
                  "usesThemeStyles": true
                }
              }
            ]
          }
        ],
        "settings": {
          "trigger": "auto",
          "position": "topCenter",
          "animation": "fade",
          "hideMobile": false,
          "ribbonText": "",
          "ribbonType": "gift",
          "closeButton": true,
          "hideOverlay": false,
          "ribbonColor": "#000000",
          "displayCount": 2,
          "trafficShare": 0.5,
          "ribbonBgColor": "#000000",
          "ribbonPosition": "bottomLeft",
          "selectedCities": [],
          "triggerSeconds": 3,
          "closeButtonColor": "rgb(242, 248, 248)",
          "displayFrequency": 1,
          "locationStrategy": "SHOW_ALL",
          "triggerClickType": "",
          "selectedCountries": [],
          "triggerClickTarget": "",
          "closeOnOverlayClick": false,
          "triggerScrollPercent": 20,
          "displayFrequencyPeriod": "hour"
        },
        "styling": {
          "maxWidth": "100vw",
          "borderRadius": 0,
          "backgroundSize": "cover",
          "backgroundColor": "rgb(88,182,179)",
          "backgroundImage": "",
          "backgroundPosition": "center"
        },
        "theme": {
          "rank": {
            "baseColor": "rgba(208, 208, 208, 1)",
            "fontFamily": "Roboto"
          },
          "text": {
            "color": "rgba(0, 0, 0, 1)",
            "fontFamily": "Roboto"
          },
          "field": {
            "color": "rgba(0, 0, 0, 1)",
            "fontFamily": "Roboto",
            "borderColor": "rgba(208, 208, 208, 1)",
            "backgroundColor": "rgb(255, 255, 255)"
          },
          "image": {
            "backgroundColor": "rgba(0, 0, 0, 0)"
          },
          "wheel": {
            "color": "rgba(24, 23, 26, 1)",
            "fontFamily": "Roboto",
            "borderColor": "rgba(208, 208, 208, 1)",
            "backgroundColor": "rgb(255, 255, 255)",
            "secondaryBackgroundColor": "rgb(232, 232, 232)"
          },
          "button": {
            "color": "rgb(255, 255, 255)",
            "fontFamily": "Roboto",
            "borderColor": "rgba(0, 0, 0, 1)",
            "backgroundColor": "rgba(24, 23, 26, 1)"
          },
          "social": {
            "color": "rgba(0, 0, 0, 1)"
          },
          "spacer": {
            "color": "rgba(0, 0, 0, 0)"
          },
          "survey": {
            "color": "rgba(0, 0, 0, 1)",
            "fontFamily": "Roboto",
            "borderColor": "rgba(208, 208, 208, 1)",
            "backgroundColor": "rgb(232, 232, 232)"
          },
          "voucher": {
            "color": "rgba(0, 0, 0, 1)",
            "fontFamily": "Roboto",
            "borderColor": "rgba(208, 208, 208, 1)"
          },
          "checkbox": {
            "color": "rgba(24, 23, 26, 1)",
            "borderColor": "rgba(208, 208, 208, 1)",
            "checkboxColor": "rgba(24, 23, 26, 1)",
            "backgroundColor": "rgb(255, 255, 255)"
          },
          "headline": {
            "color": "rgba(24, 23, 26, 1)",
            "fontFamily": "Roboto"
          },
          "textarea": {
            "color": "rgba(0, 0, 0, 1)",
            "fontFamily": "Roboto",
            "borderColor": "rgba(208, 208, 208, 1)",
            "backgroundColor": "rgb(255, 255, 255)"
          },
          "countdown": {
            "color": "rgba(0, 0, 0, 1)",
            "fontFamily": "Roboto",
            "borderColor": "rgba(208, 208, 208, 1)"
          }
        },
        "active": true,
        "created_at": "2025-07-14T13:11:49.495162+03:00",
        "updated_at": "2025-08-06T14:21:18.991722+03:00"
      }
    }
  },
  filters: {
    snakeToTitleCase: function (value) {
      if (!value) return ''
      return value.split('-').map(function (item) {
        return item.charAt(0).toUpperCase() + item.substring(1)
      }).join(' ')
    },
  },
  methods: {
    triggerPreviewWidget(id) {
      this.$emit('preview-widget', id)
    },
  },
}
