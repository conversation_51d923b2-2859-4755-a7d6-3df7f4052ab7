<template>
  <div>
    <a v-if="photoSwipeActive && imageAttributes !== null"
       itemprop="contentUrl"
       :href="getImageOfSize(imageAttributes, responsiveSize.fullScreen)"
       :data-pswp-width="widthAttribute"
       :data-pswp-height="heightAttribute"
       :data-pswp-webp-src="webpURL"
       :data-caption="alternativeText">
      <ResponsivePicture :imgClass="(fixedHeight ? 'h-72 ' : '') + 'md:h-72 lg:h-80 xl:h-[400px] md:w-auto'"
                         :baseHeightOverride="fixedHeight"
                         :isImgFullscreen="true"
                         :alt="alternativeText"
                         :smallURL="getImageOfSize(imageAttributes, 'small')"
                         :mediumURL="getImageOfSize(imageAttributes, 'medium')"
                         :largeURL="getImageOfSize(imageAttributes, 'large')"
                         :xlargeURL="getImageOfSize(imageAttributes, 'xlarge')"/>
    </a>

    <!-- Render this element when photoSwipeActive is false -->
    <ResponsivePicture v-else-if="imageAttributes !== null"
                       :imgClass="(fixedHeight ? 'h-72 ' : '') + 'md:h-72 lg:h-80 xl:h-[400px] md:w-auto'"
                       :baseHeightOverride="fixedHeight"
                       :isImgFullscreen="true"
                       :alt="slide.attributes.alternativeText"
                       :smallURL="getImageOfSize(imageAttributes, 'small')"
                       :mediumURL="getImageOfSize(imageAttributes, 'medium')"
                       :largeURL="getImageOfSize(imageAttributes, 'large')"
                       :xlargeURL="getImageOfSize(imageAttributes, 'xlarge')"/>
  </div>
</template>

<script>
import ImageMeta from "@/mixins/imageMeta.js";

export default {
  name: "SwiperCarrouselSlide",
  props: {
    slide: {
      type: Object,
      default: false,
    },
    photoSwipeActive: {
      type: Boolean,
      default: false,
    },
    fixedHeight: {
      type: Boolean,
      default: false,
    },
  },
  mixins: [ImageMeta],
  computed: {
    imageAttributes() {
      return this.getAttribute(this.slide, 'attributes');
    },
  },
}
</script>
