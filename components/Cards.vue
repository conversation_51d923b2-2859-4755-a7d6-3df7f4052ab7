<template>
  <div>
    <div class="mt-2 mx-auto grid justify-center gap-4 sm:grid-cols-2 md:max-w-[64rem] md:grid-cols-3">
      <div
        v-for="(card, index) in cards"
        :key="index"
        class="relative overflow-hidden rounded-lg text-body-black bg-body-gray-dark"
        :class="{'!text-body-gray !bg-transparent border border-onvocado-primary': darkBg, '!bg-body-gray': darker}">
        <div class="flex min-h-[160px] flex-col justify-between rounded-md p-6">
          <div class="space-y-3 md:space-y-6">
            <h3 class="font-bold">{{ card.title }}</h3>
            <div class="markdown leading-loose"
                 v-html="card.description"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Cards",
  props: {
    cards: {
      type: Array,
      required: true,
    },
    darkBg: {
      type: Boolean,
      default: false,
    },
    darker: {
      type: Boolean,
      default: false,
    }
  },
};
</script>
