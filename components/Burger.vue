<template>
  <div id="menuToggle" class="my-auto">
    <span :class="['bg-body-black dark:bg-body-gray', { 'active bg-body-gray': menuOpen}]"></span>
    <span :class="['bg-body-black dark:bg-body-gray', { 'hidden': menuOpen}]"></span>
    <span :class="['bg-body-black dark:bg-body-gray', { 'active bg-body-gray': menuOpen}]"></span>
  </div>
</template>

<script>
export default {
   props: {
    menuOpen: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style lang="scss" scoped>
#menuToggle {
  display: inline-block;
  -webkit-user-select: none;
  user-select: none;

  span {
    display: block;
    height: 3px;
    margin-bottom: 5px;
    position: relative;
    border-radius: 3px;
    z-index: 1;
    transform-origin: 4px 0;
    transition: all 0.5s cubic-bezier(0.77, 0.2, 0.05, 1.0);

    &:last-of-type {
      margin-bottom: 0;
    }

    &:nth-child(1).active {
      transform: rotate(45deg) translate(-2px, -4px);
    }

    &:nth-child(2).hidden {
      opacity: 0;
      transform: rotate(0deg) scale(0.2, 0.2);
    }

    &:nth-child(3).active {
      transform: rotate(-45deg) translate(0, -1px);
    }
  }
}
</style>
