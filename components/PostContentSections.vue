<template>
  <div>
    <!-- For components that should be client-only -->
    <client-only v-if="isClientOnly(zone.__typename)">
      <component :is="zone.__typename" :zone="zone" :key="zone.id"></component>
    </client-only>

    <!-- For other components -->
    <component v-else :is="zone.__typename" :zone="zone" :key="zone.id"></component>
  </div>
</template>

<script>
export default {
  name: "PostContentSections",
  props: {
    zone: Object,
  },
  methods: {
    isClientOnly(componentName) {
      // List of components that should only render on the client side
      const clientOnlyComponents = ['ComponentPostImageSection']; // Add more names as required
      return clientOnlyComponents.includes(componentName);
    }
  }
};
</script>

