<template>
  <section class="relative">
    <div class="grid lg:grid-cols-3 grid-cols-1 gap-10 mt-20">
      <div v-for="(plan, index) in availablePlans"
           :key="index"
           class="w-full max-w-md mx-auto relative flex flex-col rounded-xl bg-body-gray-dark">
        <div v-if="plansMeta.tagline[plan.type.toLowerCase()]" class="absolute top-0 inset-x-0">
          <div class="flex justify-center">
            <span
              class="px-2 py-1 -mt-3 text-xs font-medium border border-gray-700 text-white bg-black rounded-md capitalize">
              {{ $t(`plans.tagline.${plan.type.toLowerCase()}`) }}
            </span>
          </div>
        </div>

        <div class="text-center pt-10">
          <h5 class="text-xl font-semibold">{{ plan.name }}</h5>
          <h2 class="text-5xl mt-8 mb-3 items-center align-middle">
            <sup v-if="plan.id !== 'basic'" class="text-2xl align-middle">
              {{ plan.currency === 'eur' ? '€' : '$' }}
            </sup>
            {{ plan.id !== 'basic' ? parseInt(plan.price) : $t('plans.free') }}
          </h2>
          <span>
            {{
              plan.id !== 'basic'
                ? (billingPeriod !== 'yearly' ? $t('plans.perMonth') : $t('plans.perYear'))
                : '&#8205;'
            }}
          </span>
        </div>

        <div class="pt-10 px-10 flex items-center justify-center">
          <div class="max-w-sm">
            <p class="text-sm">{{ $t(`plans.description.${plan.type.toLowerCase()}`) }}</p>
          </div>
        </div>

        <div class="p-8 flex items-center">
          <ul class="mb-2 m-auto text-left text-sm">
            <li class="my-2">
              <h5 class="text-base font-extrabold dark:text-gray-300">
                {{ formatNumber(plan.maxPageViews) }} {{ $t('plans.visitorsPerMonth') }}
              </h5>
            </li>
            <li class="my-2">
              <h5 class="text-base font-extrabold dark:text-gray-300">
                {{ $t('plans.maxDomains', { count: formatNumber(plan.maxDomains) }) }}
                {{ plan.maxDomains > 1 ? $t('plans.domains') : $t('plans.domain') }}
              </h5>
            </li>
            <li class="my-2">
              <h5 class="text-base font-extrabold dark:text-gray-300">
                {{ plan.maxCampaigns === -1 ? $t('plans.unlimited') : $t('plans.maxCampaigns', { count: formatNumber(plan.maxCampaigns) }) }}
              </h5>
            </li>

            <li class="my-2">
              <h5 class="text-base font-normal dark:text-gray-300">
                {{ $t('plans.features.detailed_analytics') }}
              </h5>
            </li>

            <li class="my-2">
              <h5 class="text-base font-normal dark:text-gray-300">
                {{ $t('plans.features.multi_step') }}
                <el-tooltip placement="top">
                  <template #content>
                    {{ $t('plans.tooltips.multi_step') }}
                  </template>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </h5>
            </li>

            <li v-for="(feature, featureIndex) in plan.features" :key="featureIndex" class="my-2">
              <h5 class="text-base font-normal dark:text-gray-300">
                {{ $t(`plans.features.${feature.toLowerCase()}`) }}
                <el-tooltip v-if="feature === 'A/B_TESTING'" placement="top">
                  <template #content>
                    {{ $t('plans.tooltips.a/b_testing') }}
                  </template>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </h5>
            </li>

            <li v-if="!plan.features.includes('A/B_TESTING')" class="my-2">
              <h5 class="text-base font-normal line-through dark:text-gray-300">
                {{ $t('plans.features.a/b_testing') }}
                <el-tooltip placement="top">
                  <template #content>
                    {{ $t('plans.tooltips.a/b_testing') }}
                  </template>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </h5>
            </li>
            <li v-if="!plan.features.includes('NO_WATERMARK')" class="my-2">
              <h5 class="text-base font-normal line-through dark:text-gray-300">
                {{ $t('plans.features.no_watermark') }}
              </h5>
            </li>
            <li v-if="!plan.features.includes('PRIORITY_SUPPORT')" class="my-2">
              <h5 class="text-base font-normal dark:text-gray-300">
                {{ $t('plans.basic_support') }}
              </h5>
            </li>
          </ul>
        </div>

        <div class="mb-2 p-5 mt-auto flex justify-center">
          <a href="https://app.onvocado.com/register" class="py-3 px-6 button big">
            {{ $t('plans.getStarted') }}
          </a>
        </div>
      </div>
    </div>
    <h5 class="text-center font-medium mt-14">
      {{ $t('plans.customPlanPrompt') }} <a :href="$localePathWithTrailingSlash('/contact/')" class="text-onvocado-primary">{{ $t('plans.getInTouch') }}</a>
    </h5>
  </section>
</template>

<script>
import plansMixins from '@/mixins/plans'

export default {
  name: 'Plans',
  mixins: [plansMixins],
  props: {
    availablePlans: {
      type: Array,
      required: true,
    },
    billingPeriod: {
      type: String,
      required: true,
    },
    currency: {
      type: String,
      required: true,
    },
  },
  methods: {
    formatNumber(value) {
      if (value === -1) {
        return ''
      }
      return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },
  },
}
</script>
