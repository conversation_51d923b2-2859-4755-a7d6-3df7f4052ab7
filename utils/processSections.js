
/**
 * Processes the sections of a blog post to extract and format content for RSS.
 * @param {Array} sections - Array of section objects from the blog post.
 * @returns {string} - Formatted HTML string suitable for RSS content.
 */
export function processSections(sections) {
  let content = ''

  sections.forEach(section => {
    switch (section.__typename) {
      case 'ComponentPostTextSection':
        if (section.title) {
          content += `<h2>${escapeHtml(section.title)}</h2>`
        }
        if (section.content) {
          content += `${section.content}`
        }
        break

      case 'ComponentPostImageSection':
        if (section.image?.data?.attributes?.url) {
          content += `<img src="${section.image.data.attributes.url}" alt="${escapeHtml(section.image.data.attributes.alternativeText || '')}" />`
        }
        break

      case 'ComponentPostImageTextSection':
        if (section.title) {
          content += `<h2>${escapeHtml(section.title)}</h2>`
        }
        if (section.content) {
          content += `${section.content}`
        }
        if (section.image?.data?.attributes?.url) {
          content += `<img src="${section.image.data.attributes.url}" alt="${escapeHtml(section.image.data.attributes.alternativeText || '')}" />`
        }
        break

      case 'ComponentPostSliderSection':
        // For sliders, you might want to include the first image or skip
        if (section.images?.data?.length > 0) {
          const firstImage = section.images.data[0].attributes
          content += `<img src="${firstImage.url}" alt="${escapeHtml(firstImage.alternativeText || '')}" />`
        }
        break

      // Add more cases as needed for other section types

      default:
        // Handle unknown section types or skip
        break
    }
  })

  return content
}

/**
 * Escapes HTML special characters to prevent XSS attacks.
 * @param {string} unsafe - The string to escape.
 * @returns {string} - The escaped string.
 */
function escapeHtml(unsafe) {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;")
}
