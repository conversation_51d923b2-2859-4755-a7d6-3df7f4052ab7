/**
 * Utility function to retry Apollo queries with exponential backoff
 * Handles network errors like "socket hang up" common with Heroku-hosted APIs
 *
 * @param {Object} client - Apollo client instance
 * @param {Object} options - Query options (query, variables, etc.)
 * @param {Object} retryOptions - Retry configuration
 * @returns {Promise} - Query result or fallback data
 */
export async function retryApolloQuery(client, options, retryOptions = {}) {
  const {
    maxAttempts = 3,
    initialDelay = 1000,
    maxDelay = 10000,
    factor = 2,
    onRetry = () => {},
    fallbackData = { data: {} }
  } = retryOptions;

  let lastError;
  let delay = initialDelay;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      // Attempt the query
      const result = await client.query(options);
      return result;
    } catch (error) {
      lastError = error;

      // Check if we should retry
      const isNetworkError = error.networkError !== undefined;
      const isHerokuError = error.networkError?.statusCode === 503 &&
                           error.networkError?.bodyText?.includes('herokucdn.com/error-pages');
      const isLastAttempt = attempt === maxAttempts;

      // Log the error
      console.error(`Apollo query attempt ${attempt}/${maxAttempts} failed:`, error.message);

      if (isHerokuError) {
        console.warn('Detected Heroku application error (503). Server might be in maintenance or crashed.');
      }

      if (isLastAttempt) {
        console.warn('All retry attempts failed. Using fallback data.');
        break;
      }

      // Call the onRetry callback
      onRetry(error, attempt, delay);

      // Wait before the next attempt
      await new Promise(resolve => setTimeout(resolve, delay));

      // Increase the delay for the next attempt (exponential backoff with jitter)
      delay = Math.min(delay * factor * (0.8 + Math.random() * 0.4), maxDelay);
    }
  }

  // If we're in static generation mode, return fallback data instead of throwing
  if (process.static && process.client === false) {
    console.warn('Continuing static generation with fallback data');
    return fallbackData;
  }

  // Otherwise, rethrow the last error
  throw lastError;
}

export default retryApolloQuery;
