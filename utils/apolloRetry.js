/**
 * Retry utility for Apollo GraphQL queries with exponential backoff
 * Handles network errors like "socket hang up" common with Heroku-hosted APIs
 */

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms))

const isRetryableError = (error) => {
  if (!error) return false
  
  // Check for network errors
  if (error.networkError) {
    const networkError = error.networkError
    
    // Retry on connection issues
    if (networkError.code === 'ECONNRESET' || 
        networkError.code === 'ENOTFOUND' ||
        networkError.code === 'ECONNREFUSED' ||
        networkError.message?.includes('socket hang up') ||
        networkError.message?.includes('timeout') ||
        networkError.message?.includes('ETIMEDOUT') ||
        networkError.message?.includes('ECONNRESET')) {
      return true
    }
    
    // Retry on HTTP 5xx errors (server errors)
    if (networkError.statusCode >= 500) {
      return true
    }
    
    // Retry on HTTP 429 (rate limiting)
    if (networkError.statusCode === 429) {
      return true
    }
  }
  
  // Don't retry on GraphQL errors or client errors (4xx)
  return false
}

const calculateDelay = (attempt, baseDelay = 1000, maxDelay = 30000) => {
  // Exponential backoff with jitter
  const exponentialDelay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay)
  const jitter = Math.random() * 0.1 * exponentialDelay
  return exponentialDelay + jitter
}

/**
 * Retry wrapper for Apollo queries
 * @param {Function} queryFn - Function that returns a Promise (Apollo query)
 * @param {Object} options - Retry options
 * @param {number} options.maxAttempts - Maximum number of retry attempts (default: 5)
 * @param {number} options.baseDelay - Base delay in milliseconds (default: 1000)
 * @param {number} options.maxDelay - Maximum delay in milliseconds (default: 30000)
 * @param {Function} options.onRetry - Callback function called on each retry
 * @returns {Promise} - Promise that resolves with the query result or rejects with the final error
 */
export const retryQuery = async (queryFn, options = {}) => {
  const {
    maxAttempts = 5,
    baseDelay = 1000,
    maxDelay = 30000,
    onRetry = () => {}
  } = options

  let lastError = null

  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      const result = await queryFn()
      
      // If we get here, the query succeeded
      if (attempt > 0) {
        console.log(`Query succeeded after ${attempt + 1} attempts`)
      }
      
      return result
    } catch (error) {
      lastError = error
      
      // Check if this is the last attempt
      if (attempt === maxAttempts - 1) {
        console.error(`Query failed after ${maxAttempts} attempts:`, error.message)
        throw error
      }
      
      // Check if the error is retryable
      if (!isRetryableError(error)) {
        console.error('Non-retryable error encountered:', error.message)
        throw error
      }
      
      // Calculate delay for next attempt
      const delay = calculateDelay(attempt, baseDelay, maxDelay)
      
      console.log(`Query attempt ${attempt + 1} failed: ${error.message}. Retrying in ${Math.round(delay)}ms...`)
      
      // Call the onRetry callback
      onRetry(error, attempt + 1, delay)
      
      // Wait before retrying
      await sleep(delay)
    }
  }
  
  // This should never be reached, but just in case
  throw lastError
}

/**
 * Convenience wrapper for Apollo client queries
 * @param {Object} apolloClient - Apollo client instance
 * @param {Object} queryOptions - Apollo query options (query, variables, etc.)
 * @param {Object} retryOptions - Retry options
 * @returns {Promise} - Promise that resolves with the query result
 */
export const retryApolloQuery = (apolloClient, queryOptions, retryOptions = {}) => {
  return retryQuery(
    () => apolloClient.query(queryOptions),
    retryOptions
  )
}

export default retryQuery
