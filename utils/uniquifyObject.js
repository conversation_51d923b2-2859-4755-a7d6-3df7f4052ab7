import UIDGenerator from './UIDGenerator'

/**
 * Generates a unique id for each property inside the campaign
 *
 * @returns {json} with unique ids
 */
export default function(campaign) {
  const UIDCampaign = uniqueifyObjectByKey(campaign, 'id')

  function uniqueifyObjectByKey(obj, key) {
    let objects = []

    for (const i in obj) {
      // eslint-disable-next-line
      if (!obj.hasOwnProperty(i)) continue
      if (typeof obj[i] === 'object') {
        objects = objects.concat(uniqueifyObjectByKey(obj[i], key))
      } else if (i === key) {
        obj[key] = UIDGenerator()
      }
    }
    return obj
  }

  return UIDCampaign
}
