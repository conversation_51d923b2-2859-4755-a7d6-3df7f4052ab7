export default ({ graphQLErrors, networkError, operation, forward }, nuxtContext) => {
  console.log('=== Apollo Global Error Handler ===')

  if (networkError) {
    console.error('Network Error:', {
      message: networkError.message,
      statusCode: networkError.statusCode,
      response: networkError.response,
      bodyText: networkError.bodyText,
      operation: operation.operationName,
      variables: operation.variables
    })

    // Log the actual response body if it's HTML (common cause of JSON parse errors)
    if (networkError.bodyText && networkError.bodyText.includes('<')) {
      console.error('Response appears to be HTML instead of JSON:', networkError.bodyText.substring(0, 500))
    }
  }

  if (graphQLErrors && graphQLErrors.length > 0) {
    console.error('GraphQL Errors:', graphQLErrors.map(error => ({
      message: error.message,
      locations: error.locations,
      path: error.path,
      extensions: error.extensions
    })))
  }

  console.log('Operation details:', {
    operationName: operation.operationName,
    variables: operation.variables,
    query: operation.query.loc?.source?.body?.substring(0, 200) + '...'
  })

  console.log('Environment info:', {
    backendUrl: process.env.BACKEND_URL,
    nodeEnv: process.env.NODE_ENV,
    isClient: process.client,
    isServer: process.server
  })
}
