export default function ({ $config, isDev }) {
  // Validate required environment variables
  const requiredConfig = {
    backendUrl: $config.backendUrl,
    baseUrl: $config.baseUrl
  }

  const missingConfig = Object.entries(requiredConfig)
    .filter(([key, value]) => !value)
    .map(([key]) => key)

  if (missingConfig.length > 0) {
    const errorMessage = `Missing required configuration: ${missingConfig.join(', ')}`
    console.error('Environment validation failed:', errorMessage)
    
    if (!isDev) {
      throw new Error(errorMessage)
    }
  }

  // Log configuration in development
  if (isDev) {
    console.log('Environment configuration:', {
      backendUrl: $config.backendUrl,
      baseUrl: $config.baseUrl,
      nodeEnv: process.env.NODE_ENV
    })
  }

  // Validate backend URL format
  if ($config.backendUrl) {
    try {
      new URL($config.backendUrl)
    } catch (error) {
      const errorMessage = `Invalid backend URL format: ${$config.backendUrl}`
      console.error(errorMessage)
      if (!isDev) {
        throw new Error(errorMessage)
      }
    }
  }
}
