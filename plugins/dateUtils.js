import { format } from 'date-fns';
import { enUS, bg, es } from 'date-fns/locale';

export default (context, inject) => {
  const formatDate = (date) => {
    const formatStr = 'MMMM dd, yyyy'
    const locale = getLocaleObject(context.app.i18n?.locale || 'en')
    return format(new Date(date), formatStr, {locale})
  }

  const getLocaleObject = (localeStr) => {
    switch (localeStr) {
      case 'en':
        return enUS;
      case 'es':
        return es;
      case 'bg':
        return bg;
      default:
        return enUS;
    }
  }

  // Inject formatDate into the context so it's available globally.
  inject('formatDate', formatDate);
}
