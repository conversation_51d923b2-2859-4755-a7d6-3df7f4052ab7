# Troubleshooting Guide

## GraphQL "Unexpected token < in JSON at position 0" Error

This error occurs when the GraphQL client expects JSON but receives HTML instead. This typically happens during static site generation in AWS Amplify.

### Common Causes:

1. **Incorrect Backend URL**: The `BACKEND_URL` environment variable is not set or points to the wrong endpoint
2. **Server Error**: The GraphQL server is returning an error page (404, 500, etc.) instead of JSON
3. **Network Issues**: DNS resolution problems or connectivity issues
4. **CORS Issues**: Cross-origin request blocked by the server
5. **Authentication**: Missing or invalid authentication headers

### Solutions:

#### 1. Verify Environment Variables
Ensure the following environment variables are set in AWS Amplify:
```
BACKEND_URL=https://your-graphql-endpoint.com
BASE_URL=https://your-site-domain.com
```

#### 2. Test GraphQL Endpoint
Test the endpoint manually:
```bash
curl -X POST https://your-graphql-endpoint.com/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "{ __typename }"}'
```

#### 3. Check Server Logs
Look for errors in your GraphQL server logs that might indicate:
- Database connection issues
- Authentication problems
- Rate limiting
- Server overload

#### 4. Verify Network Connectivity
Ensure AWS Amplify can reach your GraphQL server:
- Check firewall rules
- Verify DNS resolution
- Test from AWS region where Amplify is running

#### 5. Add Error Handling
The codebase now includes improved error handling:
- Better error logging in Apollo client
- Graceful fallbacks in asyncData methods
- Environment validation

### Debugging Steps:

1. Check the Apollo error handler logs for detailed error information
2. Verify the exact URL being called in the logs
3. Test the GraphQL endpoint independently
4. Check AWS Amplify build logs for network errors
5. Verify environment variables are correctly set

### Prevention:

1. Always use try-catch blocks in asyncData methods
2. Implement proper error boundaries
3. Add health checks for external dependencies
4. Monitor GraphQL server uptime and performance
