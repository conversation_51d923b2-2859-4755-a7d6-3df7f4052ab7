{"__schema": {"types": [{"kind": "UNION", "name": "BlogPostSectionsDynamicZone", "possibleTypes": [{"name": "ComponentPostChallengeSection"}, {"name": "ComponentPostImageSection"}, {"name": "ComponentPostSliderSection"}, {"name": "ComponentPostTextSection"}, {"name": "Error"}]}, {"kind": "UNION", "name": "GenericMorph", "possibleTypes": [{"name": "Blog"}, {"name": "BlogPost"}, {"name": "ComponentPageSpecificDoodle"}, {"name": "ComponentPageSpecificMission"}, {"name": "ComponentPageSpecificProcess"}, {"name": "ComponentPageSpecificServices"}, {"name": "ComponentPageSpecificTeam"}, {"name": "ComponentPostChallengeSection"}, {"name": "ComponentPostImageSection"}, {"name": "ComponentPostQuoteSection"}, {"name": "ComponentPostSliderSection"}, {"name": "ComponentPostTextSection"}, {"name": "ComponentSharedMetaSocial"}, {"name": "ComponentSharedSeo"}, {"name": "Home"}, {"name": "I18NLocale"}, {"name": "News"}, {"name": "NewsPost"}, {"name": "Philosophy"}, {"name": "Services"}, {"name": "Tag"}, {"name": "Team"}, {"name": "UploadFile"}, {"name": "UploadFolder"}, {"name": "UsersPermissionsPermission"}, {"name": "UsersPermissionsRole"}, {"name": "UsersPermissionsUser"}, {"name": "Work"}, {"name": "WorkPost"}]}, {"kind": "UNION", "name": "NewsPostSectionsDynamicZone", "possibleTypes": [{"name": "ComponentPostChallengeSection"}, {"name": "ComponentPostImageSection"}, {"name": "ComponentPostSliderSection"}, {"name": "ComponentPostTextSection"}, {"name": "Error"}]}, {"kind": "UNION", "name": "WorkPostSectionsDynamicZone", "possibleTypes": [{"name": "ComponentPostChallengeSection"}, {"name": "ComponentPostImageSection"}, {"name": "ComponentPostQuoteSection"}, {"name": "ComponentPostSliderSection"}, {"name": "ComponentPostTextSection"}, {"name": "Error"}]}]}}