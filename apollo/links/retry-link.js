import { RetryLink } from 'apollo-link-retry'

export default new RetryLink({
  delay: {
    initial: 1000, // Start with 1 second delay
    max: 30000,    // Maximum delay of 30 seconds
    jitter: true   // Add randomness to prevent thundering herd
  },
  attempts: {
    max: 5,        // Maximum 5 retry attempts
    retryIf: (error, _operation) => {
      // Retry on network errors
      if (error && error.networkError) {
        const networkError = error.networkError
        
        // Retry on specific network errors
        if (networkError.code === 'ECONNRESET' || 
            networkError.code === 'ENOTFOUND' ||
            networkError.code === 'ECONNREFUSED' ||
            networkError.message.includes('socket hang up') ||
            networkError.message.includes('timeout') ||
            networkError.message.includes('ETIMEDOUT')) {
          console.log(`Retrying request due to network error: ${networkError.message}`)
          return true
        }
        
        // Retry on HTTP 5xx errors (server errors)
        if (networkError.statusCode >= 500) {
          console.log(`Retrying request due to server error: ${networkError.statusCode}`)
          return true
        }
        
        // Retry on HTTP 429 (rate limiting)
        if (networkError.statusCode === 429) {
          console.log('Retrying request due to rate limiting')
          return true
        }
      }
      
      // Don't retry on GraphQL errors or client errors (4xx)
      return false
    }
  }
})
