import { onError } from 'apollo-link-error'

export default onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path }) => {
      console.error(
        `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
      )
    })
  }

  if (networkError) {
    console.error(`[Network error]: ${networkError.message}`)

    // Check for Heroku application error
    if (networkError.statusCode === 503 &&
        networkError.bodyText &&
        networkError.bodyText.includes('herokucdn.com/error-pages')) {
      console.error('Heroku application error detected. The backend server might be down or in maintenance mode.')
    }

    // For static site generation, we want to continue rather than fail
    if (process.static && process.client === false) {
      console.warn('Continuing static generation despite GraphQL error')
      // Return empty data to allow the build to continue
      return forward(operation).map(data => {
        return { data: {} };
      });
    }
  }
})
