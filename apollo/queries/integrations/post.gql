query($slug: String!, $locale: I18NLocaleCode!){
  integrationPosts(filters: { slug: {eq: $slug}}, locale: $locale) {
    data {
      id
      attributes {
        createdAt
        updatedAt
        title
        subtitle
        slug
        publishedDate
        thumbnail {
          data {
            attributes {
              formats
              blurhash
              url
              ext
              alternativeText
            }
          }
        }
        author {
          data {
            attributes {
             name
              description
              image {
                data {
                  attributes {
                    formats
                    blurhash
                    url
                    ext
                    alternativeText
                  }
                }
              }
            }
          }
        }
        sections {
          __typename
          ... on ComponentPostTextSection {
            title
            content
          }
          ... on ComponentPostImageSection {
            image {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                }
              }
            }
          }
          ... on ComponentPostImageTextSection {
            title
            content
            imageFirst
            isColumn
            image {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                }
              }
            }
          }
          ... on ComponentPostSliderSection {
            images {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                }
              }
            }
          }
          ... on ComponentPostSliderSection {
            images {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                }
              }
            }
          }
          ... on ComponentGeneralFaqSection {
            title
            questions {
              question
              answer
            }
          }
          ... on ComponentGeneralCardsSection {
            dark
            content
            cards {
              title
              description
            }
          }
          ... on ComponentGeneralCtaSection {
            title
            subtitle
            details
            sideText
            cards {
              title
              description
            }
            button {
              text
              link
            }
          }
          ... on ComponentGeneralStepsSection {
            title
            steps {
              title
              details
            }
            button {
              text
              link
            }
          }
          ... on ComponentGeneralFeaturesGridSection {
            title
            subtitle
            features {
              title
              details
              icon {
                data {
                  attributes {
                    url
                    alternativeText
                  }
                }
              }
            }
          }
          ... on ComponentGeneralImageTextSection {
            title
            subtitle
            details
            imageFirst
            videoOnce
            image {
              data {
                attributes {
                  url
                  provider_metadata
                  mime
                  formats
                  alternativeText
                }
              }
            }
          }
          ... on ComponentGeneralTestimonialsSection {
            title
            testimonials(pagination: {limit:50}) {
              name
              role
              text
              image {
                data {
                  attributes {
                    formats
                    alternativeText
                  }
                }
              }
            }
          }
          ... on ComponentGeneralImageSection {
            title
            details
            image {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                }
              }
            }
          }
          ... on ComponentGeneralSupportSection {
            title
            details
          }
          ... on ComponentGeneralTitleSection {
            content
          }
        }
        SEO {
          metaTitle
          metaDescription
          metaImage {
            data {
              attributes {
                url
                alternativeText
              }
            }
          }
          type
          metaKeywords
          onvocadoPixelURL
        }
        locale
        localizations {
          data {
            id
            attributes {
              slug
              locale
            }
          }
        }
      }
    }
  }
}
