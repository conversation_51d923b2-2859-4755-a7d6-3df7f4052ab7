query IntegrationPost($locale: I18NLocaleCode!, $publicationState: PublicationState = LIVE) {
  integrationPosts(locale: $locale, publicationState:$publicationState, sort: "publishedDate:desc", pagination: { limit: -1 }) {
    data {
      id
      attributes {
        createdAt
        updatedAt
        title
        subtitle
        slug
        publishedDate
        thumbnail {
          data {
            attributes {
              url
              ext
              formats
              blurhash
              alternativeText
            }
          }
        }
      }
    }
  }
}
