query Category($locale: I18NLocaleCode!, $slug: String!) {
  blogCategories(locale: $locale, filters: { slug: { eq: $slug } }) {
    data {
      attributes {
        title
        slug
        description
        SEO {
          metaTitle
          metaDescription
          metaImage {
            data {
              attributes {
                url
                alternativeText
              }
            }
          }
          type
          metaKeywords
          onvocadoPixelURL
        }
        locale
        localizations {
          data {
            id
            attributes {
              slug
              locale
            }
          }
        }
      }
    }
  }
}
