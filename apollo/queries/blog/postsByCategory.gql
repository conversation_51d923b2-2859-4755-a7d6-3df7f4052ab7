query PostsByCategory($locale: I18NLocaleCode!, $slug: String!, $publicationState: PublicationState = LIVE) {
  blogPosts(
    locale: $locale,
    filters: { blog_categories: { slug: { eq: $slug } } },
    publicationState:$publicationState, sort: "publishedDate:desc", pagination: { limit: -1 }
  ) {
    data {
      attributes {
        createdAt
        updatedAt
        title
        subtitle
        slug
        featured
        publishedDate
        thumbnail {
          data {
            attributes {
              formats
              blurhash
              alternativeText
              url
              ext
            }
          }
        }
        blog_categories {
          data {
            attributes {
              title
            }
          }
        }
      }
    }
  }
}
