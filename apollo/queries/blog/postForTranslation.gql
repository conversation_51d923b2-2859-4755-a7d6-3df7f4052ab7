query($slug: String!, $locale: I18NLocaleCode!){
  blogPosts(filters: { slug: {eq: $slug}}, locale: $locale) {
    data {
      id
      attributes {
        title
        subtitle
        slug
        thumbnail {
          data {
           id
          }
        }
        sections {
          __typename
          ... on ComponentPostTextSection {
            title
            content
          }
          ... on ComponentPostImageSection {
            image {
              data {
                id
              }
            }
          }
          ... on ComponentPostImageTextSection {
            title
            content
            image {
              data {
                id
              }
            }
          }
          ... on ComponentPostFaqSection {
            title
            description
            questions {
              question
              answer
            }
          }
        }
        SEO {
          metaTitle
          metaDescription
          metaKeywords
        }
      }
    }
  }
}
