// Used in n8n

mutation CreateBlogPost($data: BlogPostInput!) {
  createBlogPost(data: $data) {
    data {
      id
      attributes {
        createdAt
        updatedAt
        title
        subtitle
        slug
        featured
        publishedDate
        thumbnail {
          data {
            attributes {
              url
              alternativeText
              ext
            }
          }
        }
        author {
          data {
            id
          }
        }
        sections {
          __typename
          ... on ComponentPostTextSection {
            title
            content
          }
          ... on ComponentPostImageSection {
            image {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                }
              }
            }
          }
          ... on ComponentPostImageTextSection {
            title
            content
            imageFirst
            isColumn
            image {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                }
              }
            }
          }
          ... on ComponentPostSliderSection {
            images {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                }
              }
            }
          }
        }
      }
    }
  }
}
