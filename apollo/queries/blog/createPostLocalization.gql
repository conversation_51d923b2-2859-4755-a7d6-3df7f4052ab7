// Used in n8n

mutation createBlogPostLocalization($id: ID!, $data: BlogPostInput!, $locale: I18NLocaleCode!) {
  createBlogPostLocalization(id: $id, locale: $locale, data: $data) {
    data {
      id
      attributes {
        createdAt
        updatedAt
        title
        subtitle
        slug
        featured
        publishedDate
        thumbnail {
          data {
            attributes {
              url
              alternativeText
            }
          }
        }
        author {
          data {
            id
          }
        }
        sections {
          __typename
          ... on ComponentPostTextSection {
            title
            content
          }
          ... on ComponentPostImageSection {
            image {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                }
              }
            }
          }
          ... on ComponentPostImageTextSection {
            title
            content
            imageFirst
            isColumn
            image {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                }
              }
            }
          }
          ... on ComponentPostSliderSection {
            images {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                }
              }
            }
          }
        }
      }
    }
  }
}
