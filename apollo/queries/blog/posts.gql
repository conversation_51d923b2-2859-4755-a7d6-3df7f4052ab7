query BlogPost($locale: I18NLocaleCode!, $publicationState: PublicationState = LIVE) {
  blogPosts(locale: $locale, publicationState:$publicationState, sort: "publishedDate:desc", pagination: { limit: -1 }) {
    data {
      id
      attributes {
        createdAt
        updatedAt
        title
        subtitle
        slug
        featured
        publishedDate
        thumbnail {
          data {
            attributes {
              formats
              blurhash
              alternativeText
              url
              ext
            }
          }
        }
        blog_categories {
          data {
            attributes {
              title
              slug
            }
          }
        }
      }
    }
  }
}
