query($slug: String!, $locale: I18NLocaleCode!){
  blogPosts(filters: { slug: {eq: $slug}}, locale: $locale) {
    data {
      id
      attributes {
        createdAt
        updatedAt
        title
        subtitle
        slug
        featured
        publishedDate
        author {
          data {
            attributes {
             name
              description
              image {
                data {
                  attributes {
                    formats
                    blurhash
                    url
                    ext
                    alternativeText
                  }
                }
              }
            }
          }
        }
        thumbnail {
          data {
            attributes {
              formats
              blurhash
              url
              ext
              alternativeText
            }
          }
        }
        sections {
          __typename
          ... on ComponentPostTextSection {
            title
            content
          }
          ... on ComponentPostImageSection {
            image {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                  url
                  ext
                }
              }
            }
          }
          ... on ComponentPostImageTextSection {
            title
            content
            imageFirst
            isColumn
            image {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                  url
                  ext
                }
              }
            }
          }
          ... on ComponentPostSliderSection {
            images {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                  url
                  ext
                }
              }
            }
          }
          ... on ComponentPostFaqSection {
            title
            description
            questions {
              question
              answer
            }
          }
        }
        blog_categories {
          data {
            attributes {
              title
            }
          }
        }
        SEO {
          metaTitle
          metaDescription
          metaImage {
            data {
              attributes {
                url
                alternativeText
              }
            }
          }
          type
          metaKeywords
          onvocadoPixelURL
        }
        locale
        localizations {
          data {
            id
            attributes {
              slug
              locale
            }
          }
        }
      }
    }
  }
}
