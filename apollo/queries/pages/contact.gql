query Contact($locale: I18NLocaleCode!) {
  contact(locale: $locale) {
    data {
      attributes {
        createdAt
        updatedAt
        title
        details
        locale
        form {
          __typename
          ... on ComponentFormTextInput {
            label
            required
            textarea
          }
          ... on ComponentFormNumberInput {
            label
            required
          }
          ... on ComponentFormDateInput {
            label
            required
          }
          ... on ComponentFormEmailInput {
            label
            required
          }
          ... on ComponentFormSelectInput {
            label
            required
            options {
              id
              text
              value
            }
          }
        }
        SEO {
          metaTitle
          metaDescription
          metaImage {
            data {
              attributes {
                url
                alternativeText
              }
            }
          }
          type
          metaKeywords
          onvocadoPixelURL
        }
      }
    }
  }
}
