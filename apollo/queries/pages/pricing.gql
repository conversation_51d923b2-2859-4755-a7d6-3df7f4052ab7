query Pricing($locale: I18NLocaleCode!) {
  pricing(locale: $locale) {
    data {
      attributes {
        createdAt
        updatedAt
        title
        details
        locale
        sections {
          __typename
          ... on ComponentGeneralFaqSection {
            title
            questions {
              question
              answer
            }
          }
        }
        SEO {
          metaTitle
          metaDescription
          metaImage {
            data {
              attributes {
                url
                alternativeText
              }
            }
          }
          type
          metaKeywords
          onvocadoPixelURL
        }
      }
    }
  }
}
