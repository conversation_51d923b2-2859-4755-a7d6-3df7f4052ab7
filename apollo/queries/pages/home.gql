query Home($locale: I18NLocaleCode!) {
  home(locale: $locale) {
    data {
      attributes {
        createdAt
        updatedAt
        locale
        Hero {
          title
          description
          image {
            data {
              attributes {
                formats
                alternativeText
              }
            }
          }
          supportingImage {
            data {
              attributes {
                formats
                alternativeText
              }
            }
          }
        }
        sections {
          __typename
          ... on ComponentGeneralFaqSection {
            title
            questions {
              question
              answer
            }
          }
          ... on ComponentGeneralCardsSection {
            dark
            content
            cards {
              title
              description
            }
          }
          ... on ComponentGeneralCtaSection {
            title
            subtitle
            details
            sideText
            cards {
              title
              description
            }
            button {
              text
              link
            }
          }
          ... on ComponentGeneralStepsSection {
            title
            steps {
              title
              details
            }
            button {
              text
              link
            }
          }
          ... on ComponentGeneralFeaturesGridSection {
            title
            subtitle
            features {
              title
              details
              icon {
                data {
                  attributes {
                    url
                    alternativeText
                  }
                }
              }
            }
          }
          ... on ComponentGeneralImageTextSection {
            title
            subtitle
            details
            imageFirst
            videoOnce
            image {
              data {
                attributes {
                  url
                  provider_metadata
                  mime
                  formats
                  alternativeText
                }
              }
            }
          }
          ... on ComponentGeneralTestimonialsSection {
            title
            testimonials(pagination: {limit:50}) {
              name
              role
              text
              image {
                data {
                  attributes {
                    formats
                    alternativeText
                  }
                }
              }
            }
          }
          ... on ComponentGeneralImageSection {
            title
            details
            image {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                }
              }
            }
          }
          ... on ComponentGeneralSupportSection {
            title
            details
          }
          ... on ComponentGeneralTitleSection {
            content
          }
        }
        SEO {
          metaTitle
          metaDescription
          metaImage {
            data {
              attributes {
                url
                alternativeText
              }
            }
          }
          type
          metaKeywords
          onvocadoPixelURL
        }
      }
    }
  }
}
