query Roadmap($locale: I18NLocaleCode!) {
  roadmap(locale: $locale) {
    data {
      attributes {
        createdAt
        updatedAt
        title
        details
        locale
        slider {
          title
          items(pagination: { limit: 100 }) {
            title
            date
            description
          }
        }
        sections {
          __typename
          ... on ComponentGeneralFaqSection {
            title
            questions {
              question
              answer
            }
          }
          ... on ComponentGeneralCardsSection {
            dark
            content
            cards {
              title
              description
            }
          }
          ... on ComponentGeneralCtaSection {
            title
            subtitle
            details
            sideText
            cards {
              title
              description
            }
            button {
              text
              link
            }
          }
          ... on ComponentGeneralStepsSection {
            title
            steps {
              title
              details
            }
            button {
              text
              link
            }
          }
          ... on ComponentGeneralFeaturesGridSection {
            title
            subtitle
            features {
              title
              details
              icon {
                data {
                  attributes {
                    url
                    alternativeText
                  }
                }
              }
            }
          }
          ... on ComponentGeneralImageTextSection {
            title
            subtitle
            details
            imageFirst
            videoOnce
            image {
              data {
                attributes {
                  formats
                  alternativeText
                }
              }
            }
          }
          ... on ComponentPostTextSection {
            title
            content
          }
          ... on ComponentPostImageSection {
            image {
              data {
                id
              }
            }
          }
          ... on ComponentPostImageTextSection {
            title
            content
            image {
              data {
                id
              }
            }
          }
          ... on ComponentPostFaqSection {
            title
            description
            questions {
              question
              answer
            }
          }
        }
        SEO {
          metaTitle
          metaDescription
          metaImage {
            data {
              attributes {
                url
                alternativeText
              }
            }
          }
          type
          metaKeywords
          onvocadoPixelURL
        }
      }
    }
  }
}
