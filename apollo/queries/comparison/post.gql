query($slug: String!, $locale: I18NLocaleCode!){
  comparisonPosts(filters: { slug: {eq: $slug}}, locale: $locale) {
    data {
      id
      attributes {
        createdAt
        updatedAt
        publishedDate
        title
        subtitle
        slug
        sections {
          __typename
          ... on ComponentPostTextSection {
            title
            content
          }
          ... on ComponentPostImageSection {
            image {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                  url
                  ext
                }
              }
            }
          }
          ... on ComponentPostImageTextSection {
            title
            content
            imageFirst
            isColumn
            image {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                  url
                  ext
                }
              }
            }
          }
          ... on ComponentPostSliderSection {
            images {
              data {
                attributes {
                  formats
                  blurhash
                  alternativeText
                  url
                  ext
                }
              }
            }
          }
          ... on ComponentPostFaqSection {
            title
            description
            questions {
              question
              answer
            }
          }
        }
        SEO {
          metaTitle
          metaDescription
          metaImage {
            data {
              attributes {
                url
                alternativeText
              }
            }
          }
          type
          metaKeywords
          onvocadoPixelURL
        }
        locale
        localizations {
          data {
            id
            attributes {
              slug
              locale
            }
          }
        }
      }
    }
  }
}
