query($slug: String!, $locale: I18NLocaleCode!){
  templatePosts(filters: { slug: {eq: $slug}}, locale: $locale) {
    data {
      id
      attributes {
        createdAt
        updatedAt
        title
        subtitle
        content
        slug
        thumbnails {
          data {
            attributes {
              formats
              blurhash
              url
              alternativeText
            }
          }
        }
        template_categories {
          data {
            attributes {
              title
              slug
            }
          }
        }
        SEO {
          metaTitle
          metaDescription
          metaImage {
            data {
              attributes {
                url
                alternativeText
              }
            }
          }
          type
          metaKeywords
          onvocadoPixelURL
        }
        locale
        localizations {
          data {
            id
            attributes {
              slug
              locale
            }
          }
        }
      }
    }
  }
}
