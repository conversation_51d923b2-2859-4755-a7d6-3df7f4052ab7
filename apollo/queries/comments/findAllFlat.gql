query Comments($relation: String!) {
  findAllFlat(relation: $relation, filters: { approvalStatus: { eq: "APPROVED" }, blocked: { eq: false } }) {
    meta {
      pagination {
        total
        page
        start
        pageCount
      }
    }
    data {
      id
      content
      approvalStatus
      removed
      createdAt
      updatedAt
      author {
        id
        name
      }
    }
  }
}
