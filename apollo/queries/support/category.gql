query Category($locale: I18NLocaleCode!, $slug: String!) {
  supportCategories(locale: $locale, filters: { slug: { eq: $slug } }) {
    data {
      attributes {
        title
        slug
        SEO {
          metaTitle
          metaDescription
          metaImage {
            data {
              attributes {
                url
                alternativeText
              }
            }
          }
          type
          metaKeywords
          onvocadoPixelURL
        }
        locale
        localizations {
          data {
            id
            attributes {
              slug
              locale
            }
          }
        }
      }
    }
  }
}
