query SupportPost($locale: I18NLocaleCode!, $publicationState: PublicationState = LIVE) {
  supportPosts(locale: $locale, publicationState:$publicationState, sort: "publishedDate:desc", pagination: { limit: -1 }) {
    data {
      id
      attributes {
        createdAt
        updatedAt
        title
        shortTitle
        subtitle
        slug
        publishedDate
        support_category {
          data {
            attributes {
              title
              slug
              priority
            }
          }
        }
        thumbnail {
          data {
            attributes {
              formats
              blurhash
              alternativeText
            }
          }
        }
      }
    }
  }
}
