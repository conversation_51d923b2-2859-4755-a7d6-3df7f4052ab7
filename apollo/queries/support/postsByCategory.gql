query SupportPostsByCategory($locale: I18NLocaleCode!, $slug: String!, $publicationState: PublicationState = LIVE) {
  supportPosts(
    locale: $locale,
    filters: { support_category: { slug: { eq: $slug } } },
    publicationState: $publicationState,
    sort: "publishedDate:desc",
    pagination: { limit: -1 }
  ) {
    data {
      id
      attributes {
        createdAt
        updatedAt
        title
        shortTitle
        subtitle
        slug
        publishedDate
        support_category {
          data {
            attributes {
              title
              slug
              priority
            }
          }
        }
        thumbnail {
          data {
            attributes {
              formats
              blurhash
              alternativeText
            }
          }
        }
      }
    }
  }
}
