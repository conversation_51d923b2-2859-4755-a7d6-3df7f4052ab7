query($locale: I18NLocaleCode!){
  supportPosts(locale: $locale) {
    data {
      id
      attributes {
        title
        subtitle
        slug
        sections {
          __typename
          ... on ComponentPostTextSection {
            title
            content
          }
           ... on ComponentPostImageSection {
            image {
              data {
                id
              }
            }
          }
          ... on ComponentPostImageTextSection {
            title
            content
            image {
              data {
                id
              }
            }
          }
          ... on ComponentGeneralFaqSection {
            title
            questions {
              question
              answer
            }
          }
          ... on ComponentGeneralCardsSection {
            dark
            content
            cards {
              title
              description
            }
          }
          ... on ComponentGeneralCtaSection {
            title
            subtitle
            details
            sideText
            cards {
              title
              description
            }
            button {
              text
              link
            }
          }
          ... on ComponentGeneralStepsSection {
            title
            steps {
              title
              details
            }
            button {
              text
              link
            }
          }
          ... on ComponentGeneralFeaturesGridSection {
            title
            subtitle
            features {
              title
              details
              icon {
                data {
                  id
                }
              }
            }
          }
          ... on ComponentGeneralImageTextSection {
            title
            subtitle
            details
            imageFirst
            videoOnce
            image {
              data {
                id
              }
            }
          }
          ... on ComponentGeneralTestimonialsSection {
            title
            testimonials(pagination: {limit:50}) {
              name
              role
              text
              image {
                data {
                  id
                }
              }
            }
          }
          ... on ComponentGeneralImageSection {
            title
            details
            image {
              data {
                id
              }
            }
          }
          ... on ComponentGeneralSupportSection {
            title
            details
          }
          ... on ComponentGeneralTitleSection {
            content
          }
        }
        SEO {
          metaTitle
          metaDescription
          metaKeywords
          type
        }
      }
    }
  }
}
