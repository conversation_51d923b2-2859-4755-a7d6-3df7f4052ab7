// The next functions are only used if app is in preview mode
// we need them because "async asyncData" is not triggered after a redirect
export default {
  methods: {
    async fetchPost(postType, postQuery) {
      const postResponse = await this.$apolloProvider.defaultClient.query({
        query: postQuery,
        variables: {
          slug: this.$route.params.slug,
          locale: this.$i18n.locale,
        },
      });

      const postData = postResponse.data[postType].data[0];

      // // Set route parameters for localization
      // await this.$store.dispatch(
      //   "i18n/setRouteParams",
      //   Object.fromEntries(
      //     postData.attributes.localizations.data.map((el) => [
      //       el.attributes.locale,
      //       {slug: el.attributes.slug},
      //     ])
      //   )
      // );

      return postData;
    },
  },
};
