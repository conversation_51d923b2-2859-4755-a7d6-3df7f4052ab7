export const state = () => ({
  loadingAnimationFinished: false,
  previewMode: false,
  isMobile: false,
  isLenisScrollInitialized: false,
  showNav: true,
  scrollToTarget: null,
  scrollToOffset: 0,
  scrollDisabled: false,
})

export const mutations = {
  finishLoading(state) {
    state.loadingAnimationFinished = true;
  },
  setPreviewMode(state, value) {
    state.previewMode = value;
  },
  setIsMobile(state, value) {
    state.isMobile = value;
  },
  setLenisScrollInitialized(state, value) {
    state.isLenisScrollInitialized = value;
  },
  setShowNav(state, bool) {
    state.showNav = bool;
  },
  setScrollToTarget(state, options) {
    state.scrollToTarget = options.target;
    state.scrollToOffset = options.offset;
  },
  setScrollDisabled(state, value) {
    if (!state.isMobile) {
      state.scrollDisabled = value;
    } else {
      state.scrollDisabled = false
    }
  },
}

export const actions = {
  async nuxtServerInit({ dispatch }, { app }) {
    const locale = app.i18n.locale || 'en';
    await dispatch('useCases/fetchUseCases', locale);
    await dispatch('comparison/fetchComparisons', locale);
  },
  triggerScrollTo({commit}, options) {
    commit('setScrollToTarget', options);
  },
  triggerScrollDisabled({commit}, value) {
    commit('setScrollDisabled', value);
  },
  updateIsMobile({commit}, isMobile) {
    commit('setIsMobile', isMobile);
  },
}
