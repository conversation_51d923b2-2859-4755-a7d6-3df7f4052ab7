import fs from 'fs';
import {defaultLocale, locales, i18nLocales, i18nPages} from './i18n.config.js'
import {ensureTrailingSlash} from './utils/ensureTrailingSlash.js'
import {processSections} from './utils/processSections.js';
import BlogClient from './services'
import feed from './config/feed.js'
import {
  DEFAULT_AUTHOR,
  DEFAULT_NAME,
  DEFAULT_SITE_NAME,
  DEFAULT_DESCRIPTION,
  DEFAULT_PUBLISHER,
  DEFAULT_IMAGE,
  BASE_URL
} from './config/defaults.js';

const path = require('path');

const APP_BACKEND_URL = process.env.APP_BACKEND_URL || 'http://localhost:8000'
// const APP_BACKEND_URL = 'https://on.onvocado.com' // local production test

export default {
  // Disable server-side rendering: https://go.nuxtjs.dev/ssr-mode
  ssr: true,

  // Target: https://go.nuxtjs.dev/config-target
  target: 'static',

  // ----- SEO Optimization ----- https://medium.com/@dario.ferderber/optimize-nuxt-js-website-for-google-pagespeed-insights-593c92be653d
  modern: process.env.NODE_ENV === 'production',

  features: {
    store: true,
    layouts: true,
    meta: true,
    middleware: true,
    transitions: true,
    deprecations: false,
    validate: false,
    asyncData: true,
    fetch: false,
    clientOnline: true,
    clientPrefetch: true,
    componentAliases: true,
    componentClientOnly: true
  },
  // Important: Do not enable
  // https://v2.nuxt.com/docs/configuration-glossary/configuration-router#example-behavior-with-child-routes
  // This causes 404 page to not work, keep only "ensureTrailingSlash" & reference all urls with slash
  // router: {
  // trailingSlash: true,
  // },

  env: {
    appName: DEFAULT_NAME,
    BASE_URL: process.env.BASE_URL || 'http://localhost:3000',
    BACKEND_URL: process.env.BACKEND_URL || 'http://localhost:1337'
  },

  // Global CSS: https://go.nuxtjs.dev/config-css
  css: [
    {src: '~/assets/scss/main.scss', lang: 'scss'}
  ],

  // Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
  plugins: [
    {src: '~/plugins/anime.js', ssr: false},
    {src: '~/plugins/element-ui.js', ssr: true},
    {src: '~/plugins/lenis.client.js', ssr: false},
    // {src: '~/plugins/lottie', ssr: false},
    {src: '~/plugins/swiper.client.js', mode: 'client'},
    {src: '~/plugins/preview.client.js', ssr: false},
    {src: '~/plugins/preview-redirects.js', ssr: true},
    {src: '~/plugins/dateUtils.js', ssr: true},
    {src: '~/plugins/imagesLoaded.js', ssr: true},
    {src: '~/plugins/blurhash.js', ssr: true},
    {src: '~/plugins/localePathWithTrailingSlash.js', ssr: true},
    {src: '~/plugins/i18nTrailingSlashMixin.js', ssr: true},
    {src: '~/plugins/fuse.js', mode: 'client'},
    // Fake EventBus for templates
    {src: '~/plugins/event-bus.js', mode: 'client'},
    // Environment validation
    {src: '~/plugins/environment-validator.js', ssr: true},

  ],
  // Auto import components: https://go.nuxtjs.dev/config-components
  components: {
    // path: '~/components', // will get any nested components
    pathPrefix: true,
  },

  // Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
  buildModules: [
    '@nuxt/postcss8',
    '@nuxtjs/device',
    '@nuxtjs/svg',
    '@nuxt/image',
  ],

  // Modules: https://go.nuxtjs.dev/config-modules
  modules: [
    '@nuxtjs/i18n',
    '@nuxtjs/apollo',
    '@nuxtjs/axios',
    '@nuxtjs/color-mode',
    ['@nuxtjs/pwa', {
      oneSignal: false,
      icon: {source: '~/static/icon.png', sizes: [64, 192, 512], purpose: 'maskable', maskablePadding: 0},
      // meta: { author: 'Stoyan Staynov' },
      manifest: {
        name: DEFAULT_SITE_NAME,
        author: DEFAULT_AUTHOR,
        short_name: 'onvocado',
        start_url: '/',
        description: '',
        display: 'fullscreen',
        theme_color: '#171717',
        background_color: '#f8f9e8',
        apple_mobile_web_app_status_bar_style: 'black',
      },
      workbox: {
        runtimeCaching: [
          {
            // Prevent caching of blog pages or other dynamic routes
            urlPattern: '/blog/.*',
            handler: 'NetworkOnly', // Always fetch from network
          },
        ],
      },
    }],
    '@nuxtjs/sitemap',
    'cookie-universal-nuxt',
    '@nuxtjs/recaptcha',
    '@nuxtjs/feed',
  ],

  // Build Configuration: https://go.nuxtjs.dev/config-build
  build: {
    // ----- SEO Optimization -----
    aggressiveCodeRemoval: true,
    extend(config, {isClient}) {
      // Extend only webpack config for client-bundle
      if (isClient) {
        config.devtool = 'source-map'
      }
      config.resolve.alias['@utils'] = path.resolve(__dirname, './utils');

      // Only for WidgetStepPreview library
      config.module.rules.forEach(rule => {
        if (rule.test && rule.test.toString().includes('.js')) {
          // Exclude widget-step-preview.common.js from babel-loader.
          rule.exclude = rule.exclude || [];
          rule.exclude.push(path.resolve(__dirname, 'libs/widget-step-preview.common.js'));
        }
      });
    },
    // required for CommonJS imports
    transpile: ["@studio-freight/lenis", "fast-blurhash"],
    postcss: {
      plugins: {
        tailwindcss: {},
        autoprefixer: {},
      },
    },
  },

  apollo: {
    clientConfigs: {
      default: '~/apollo/client-configs/default.js'
    },
    // setup a global error handler (see below for example)
    errorHandler: '~/plugins/apollo-error-handler.js',
  },

  axios: {
    // https: true,
    baseURL: APP_BACKEND_URL, // Used as fallback if no runtime config is provided
  },

  device: {
    refreshOnResize: true
  },

  recaptcha: {
    hideBadge: true, // Hide badge element (v3 & v2 via size=invisible)
    mode: 'enterprise',       // Mode: 'base', 'enterprise'
    siteKey: '6LdwP0cpAAAAADC7TGfcKgVMcKV0JlDEKhE8UO8D',    // Site key for requests
    version: 3,    // Version
    size: 'invisible'        // Size: 'compact', 'normal', 'invisible' (v2)
  },

  // i18n module: https://i18n.nuxtjs.org/setup
  i18n: {
    baseUrl: BASE_URL,
    defaultLocale,
    parsePages: false,
    encodePaths: false,
    // seo: false,
    vueI18nLoader: true,
    trailingSlash: true,
    // skipSettingLocaleOnNavigate: true,
    // lazy: true,
    // langDir: '~/static/lang/',
    locales,
    pages: i18nPages,
    vueI18n: i18nLocales,
  },

  // Generate Configuration:
  // https://nuxtjs.org/docs/configuration-glossary/configuration-generate/
  generate: {
    crawler: false,
    // Use 404 page when page doesn't exist
    fallback: true,
    routes: async () => {
      const client = new BlogClient();
      let routes = [];
      let searchIndex = []; // Initialize the search index array

      try {
        for (const locale of locales) {
          const localeCode = locale.code === defaultLocale ? '' : `/${locale.code}`;

          // Fetch data for each content type with error handling
          try {
            // Fetch data for each content type.
            const [
              blogPostsData,
              blogCategoriesData,
              supportPostsData,
              supportCategoriesData,
              useCasePostsData,
              integrationPostsData,
              templatePostsData,
              comparisonPostsData,
            ] = await Promise.allSettled([
              client.getAllBlogPosts(locale.code).catch(err => ({ blogPosts: { data: [] } })),
              client.getAllBlogCategories(locale.code).catch(err => ({ blogCategories: { data: [] } })),
              client.getAllSupportPosts(locale.code).catch(err => ({ supportPosts: { data: [] } })),
              client.getAllSupportCategories(locale.code).catch(err => ({ supportCategories: { data: [] } })),
              client.getAllUseCasePosts(locale.code).catch(err => ({ useCasePosts: { data: [] } })),
              client.getAllIntegrationPosts(locale.code).catch(err => ({ integrationPosts: { data: [] } })),
              client.getAllTemplatePosts(locale.code).catch(err => ({ templatePosts: { data: [] } })),
              client.getAllComparisonPosts(locale.code).catch(err => ({ comparisonPosts: { data: [] } })),
            ]);

            // Process results, handling both fulfilled and rejected promises
            const contentTypes = [
              {
                data: blogPostsData.status === 'fulfilled' ? blogPostsData.value.blogPosts.data : [],
                pageKey: 'blog/_slug',
                typePrefix: 'blog_'
              },
              {
                data: blogCategoriesData.status === 'fulfilled' ? blogCategoriesData.value.blogCategories.data : [],
                pageKey: 'blog/topics/_slug',
                typePrefix: 'blog-category-',
                isCategory: true
              },
              {
                data: supportPostsData.status === 'fulfilled' ? supportPostsData.value.supportPosts.data : [],
                pageKey: 'support/_slug',
                typePrefix: 'support-'
              },
              {
                data: supportCategoriesData.status === 'fulfilled' ? supportCategoriesData.value.supportCategories.data : [],
                pageKey: 'support/topics/_slug',
                typePrefix: 'support-category-',
                isCategory: true
              },
              {
                data: useCasePostsData.status === 'fulfilled' ? useCasePostsData.value.useCasePosts.data : [],
                pageKey: 'use-cases/_slug',
                typePrefix: 'usecase-'
              },
              {
                data: integrationPostsData.status === 'fulfilled' ? integrationPostsData.value.integrationPosts.data : [],
                pageKey: 'integrations/_slug',
                typePrefix: 'integration-'
              },
              {
                data: templatePostsData.status === 'fulfilled' ? templatePostsData.value.templatePosts.data : [],
                pageKey: 'templates/_slug',
                typePrefix: 'template-'
              },
              {
                data: comparisonPostsData.status === 'fulfilled' ? comparisonPostsData.value.comparisonPosts.data : [],
                pageKey: 'comparison/_slug',
                typePrefix: 'comparison-'
              },
            ];

            // Generate routes and collect search index data for each content type
            for (const contentType of contentTypes) {
              const {data, pageKey, typePrefix, isCategory} = contentType;

              // Generate routes
              routes = routes.concat(
                data.map((item) => {
                  const slug = item.attributes.slug;
                  const path = `${localeCode}${i18nPages[pageKey][locale.code].replace(':slug', slug)}`;
                  return {
                    route: ensureTrailingSlash(path),
                  };
                })
              );

              // Skip adding categories to search index
              if (isCategory) continue;

              // Collect data for search index
              data.forEach((item) => {
                const content = processSections(item.attributes.sections || []);
                const url = ensureTrailingSlash(
                  `${localeCode}${i18nPages[pageKey][locale.code].replace(':slug', item.attributes.slug)}`
                );

                searchIndex.push({
                  id: `${typePrefix}${item.id}`, // Ensure unique ID by prefixing with content type
                  locale: locale.code,
                  title: item.attributes.title,
                  content: content,
                  url: url,
                });
              });
            }
          } catch (error) {
            console.error(`Error fetching data for locale ${locale.code}:`, error);
            // Continue with next locale
          }
        }
      } catch (error) {
        console.error('Error in route generation:', error);
        console.warn('Continuing with partial routes');
      }

      // Include /search route for each locale
      for (const locale of locales) {
        const searchRoute = ensureTrailingSlash(`${locale.code === defaultLocale ? '' : '/' + locale.code}/search`);
        routes.push(searchRoute);
      }

      // Write search index to a JSON file in the static directory
      try {
        const outputDir = path.resolve(__dirname, 'static');
        const outputFile = path.resolve(outputDir, 'search-index.json');

        // Ensure the output directory exists
        if (!fs.existsSync(outputDir)) {
          fs.mkdirSync(outputDir);
        }

        // Write the search index file
        fs.writeFileSync(outputFile, JSON.stringify(searchIndex));
      } catch (error) {
        console.error('Error writing search index:', error);
      }

      return routes;
    },
  },

  hooks: {
    // This hook runs before the static files are copied to the dist folder
    'generate:before': async (generator) => {
      console.log('Generating search-index.json...');

      const client = new BlogClient();
      let searchIndex = []; // Initialize the search index array

      for (const locale of locales) {
        const localeCode = locale.code === defaultLocale ? '' : `/${locale.code}`;

        // Fetch data for each content type
        const [
          blogPostsData,
          supportPostsData,
          useCasePostsData,
          integrationPostsData,
          templatePostsData,
          comparisonPostsData,
        ] = await Promise.all([
          client.getAllBlogPosts(locale.code),
          client.getAllSupportPosts(locale.code),
          client.getAllUseCasePosts(locale.code),
          client.getAllIntegrationPosts(locale.code),
          client.getAllTemplatePosts(locale.code),
          client.getAllComparisonPosts(locale.code),
        ]);

        // Define content types to include in the search index (excluding categories)
        const contentTypes = [
          {
            data: blogPostsData.blogPosts.data,
            pageKey: 'blog/_slug',
            typePrefix: 'blog-',
          },
          {
            data: supportPostsData.supportPosts.data,
            pageKey: 'support/_slug',
            typePrefix: 'support-',
          },
          {
            data: useCasePostsData.useCasePosts.data,
            pageKey: 'use-cases/_slug',
            typePrefix: 'usecase-',
          },
          {
            data: integrationPostsData.integrationPosts.data,
            pageKey: 'integrations/_slug',
            typePrefix: 'integration-',
          },
          {
            data: templatePostsData.templatePosts.data,
            pageKey: 'templates/_slug',
            typePrefix: 'template-',
          },
          {
            data: comparisonPostsData.comparisonPosts.data,
            pageKey: 'comparison/_slug',
            typePrefix: 'comparison-',
          },
        ];

        for (const contentType of contentTypes) {
          const {data, pageKey, typePrefix} = contentType;

          // Collect data for search index
          data.forEach((item) => {
            const content = processSections(item.attributes.sections || []);
            const url = ensureTrailingSlash(
              `${localeCode}${i18nPages[contentType.pageKey][locale.code].replace(':slug', item.attributes.slug)}`
            );

            searchIndex.push({
              id: `${typePrefix}${item.id}`, // Ensure unique ID by prefixing with content type
              locale: locale.code,
              title: item.attributes.title,
              content: content,
              url: url,
            });
          });
        }
      }

      // Define the output path for search-index.json
      const outputDir = path.resolve(__dirname, 'static');
      const outputFile = path.resolve(outputDir, 'search-index.json');

      // Ensure the output directory exists
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, {recursive: true});
      }

      // Write the search index file synchronously
      fs.writeFileSync(outputFile, JSON.stringify(searchIndex, null, 2));

      console.log(`search-index.json has been generated at ${outputFile}`);
    },
  },
  // gtm: {
  //   enabled: undefined, /* see below */
  //   id: 'GTM-WV7MV37',
  //   // GoogleAnalytics takes care of this UA-129131402-1
  //   pageTracking: false,
  // },
  colorMode: {
    preference: 'light', // default value of $colorMode.preference
    classSuffix: ''
  },
  sitemap: {
    hostname: BASE_URL,
    gzip: true,
    exclude: [
      '/use-cases',
    ],
    i18n: defaultLocale,
    trailingSlash: true
  },
  feed,
  publicRuntimeConfig: {
    baseUrl: BASE_URL,
    defaultAuthor: DEFAULT_AUTHOR,
    defaultName: DEFAULT_NAME,
    defaultSiteName: DEFAULT_SITE_NAME,
    defaultDescription: DEFAULT_DESCRIPTION,
    defaultPublisher: DEFAULT_PUBLISHER,
    defaultImage: DEFAULT_IMAGE,
    backendUrl: process.env.BACKEND_URL || 'http://localhost:1337',
    // backendUrl: 'https://cms.onvocado.com' // local production test
  },
}
