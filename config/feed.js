import BlogClient from '../services';
import { locales, defaultLocale } from '../i18n.config.js';
import { processSections } from '../utils/processSections';

import {
  DEFAULT_AUTHOR,
  DEFAULT_SITE_NAME,
  DEFAULT_DESCRIPTION,
  BASE_URL
} from './defaults.js';


/**
 * Generates a plain text summary from HTML content.
 * @param {string} content - The HTML content to summarize.
 * @returns {string} - The summarized plain text.
 */
function generateSummary(content) {
  const plainText = content.replace(/<\/?[^>]+(>|$)/g, ''); // Strip HTML tags
  const summaryLength = 200;
  return plainText.length > summaryLength ? plainText.substring(0, summaryLength) + '...' : plainText;
}

const feedConfig = [
  {
    path: '/feed.xml', // URL where the RSS feed will be accessible
    async create(feed) {
      const client = new BlogClient();
      feed.options = {
        title: DEFAULT_SITE_NAME,
        description: DEFAULT_DESCRIPTION,
        link: BASE_URL,
        image: `${BASE_URL}/images/share_1200x628.png`,
        language: defaultLocale,
      };

      const localeCodes = locales.map(locale => locale.code);

      for (const locale of localeCodes) {
        try {
          const blogPostsData = await client.getAllBlogPostsRSS(locale);
          blogPostsData.blogPosts.data.forEach(post => {
            const postUrl = `${BASE_URL}${locale === defaultLocale ? '' : `/${locale}`}/blog/${post.attributes.slug}`;
            const content = processSections(post.attributes.sections || []);
            const description =
              post.attributes.SEO?.metaDescription ||
              post.attributes.subtitle ||
              generateSummary(content);

            feed.addItem({
              title: post.attributes.title,
              id: postUrl,
              link: postUrl,
              description: description || DEFAULT_DESCRIPTION, // Use SEO.metaDescription > subtitle > content summary
              content: content,
              date: new Date(post.attributes.publishedDate || post.attributes.createdAt || Date.now()),
              author: DEFAULT_AUTHOR,
            });
          });
        } catch (error) {
          console.error(`Error fetching blog posts for locale "${locale}":`, error);
        }
      }
    },
    type: 'rss2', // Can be 'rss2', 'atom1', or 'json1'
  },
];

export default feedConfig;
