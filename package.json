{"name": "onvocado-landing", "version": "1.0.0", "private": true, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://onvocado.com"}, "scripts": {"dev": "nuxt", "dev:codegen": "graphql-codegen && nuxt", "dev:host": "nuxt --hostname '0.0.0.0' --port 8000", "build": "nuxt build", "analyze": "nuxt build --analyze", "start": "nuxt start", "generate": "nuxt generate --fail-on-error"}, "dependencies": {"@nuxtjs/apollo": "^4.0.1-rc.5", "@nuxtjs/axios": "^5.13.6", "@nuxtjs/color-mode": "^2.1.1", "@nuxtjs/feed": "^2.0.0", "@nuxtjs/i18n": "^7.3.0", "@nuxtjs/pwa": "^3.3.5", "@nuxtjs/recaptcha": "^1.1.2", "@nuxtjs/sitemap": "^2.4.0", "@nuxtjs/svg": "^0.4.0", "@studio-freight/lenis": "^1.0.33", "animejs": "^3.2.2", "apollo-cache-inmemory": "^1.6.6", "cookie-universal-nuxt": "^2.2.2", "core-js": "^3.19.3", "date-fns": "^2.30.0", "element-ui": "^2.15.14", "fast-blurhash": "^1.1.2", "fs": "^0.0.1-security", "fuse.js": "^7.0.0", "imagesloaded": "^5.0.0", "lottie-web": "^5.10.2", "nuxt": "^2.15.8", "path": "^0.12.7", "swiper": "^9.1.1", "throttle-debounce": "^5.0.0", "video.js": "^8.12.0", "vue": "^2.6.14", "vue-server-renderer": "^2.6.14", "vue-template-compiler": "^2.6.14", "webpack": "^4.46.0"}, "devDependencies": {"@graphql-codegen/cli": "^3.2.2", "@graphql-codegen/fragment-matcher": "^4.0.1", "@nuxt/image": "v0", "@nuxt/postcss8": "^1.1.3", "@nuxtjs/device": "^2.0.0", "@nuxtjs/robots": "^3.0.0", "autoprefixer": "^10.4.13", "graphql-request": "^5.1.0", "postcss": "^8.4.21", "sass": "^1.58.0", "sass-loader": "^10.1.1", "tailwindcss": "^3.4.1"}}