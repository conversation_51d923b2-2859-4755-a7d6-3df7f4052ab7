const defaultTheme = require('tailwindcss/defaultTheme')

module.exports = {
  // purge: ['./dist/*.html'],
  darkMode: 'class',
  content: [
    "./components/**/*.{js,vue}",
    "./layouts/**/*.vue",
    "./pages/**/*.vue",
    "./plugins/**/*.js",
    "./nuxt.config.js",
  ],
  theme: {
    screens: {
      // => @media (min-width: 475px) { ... }
      // 'xs': '475px',
      ...defaultTheme.screens,
    },
    extend: {
      height: {
        '18': '4.5rem',
      },
      maxWidth: (theme) => ({
        ...theme('spacing'),
      }),
      colors: {
        'body-gray': '#f8f9e8',
        'body-gray-dark': '#e7edd1',
        'body-gray-darker': '#aeb29e',
        'body-black': '#0f2815',
        'onvocado-white': '#FEFEFE',
        'onvocado-black': '#030303',
        'onvocado-primary': '#C8E85E',
        'onvocado-primary-light': '#c2e376',
        'onvocado-primary-lighter': '#e9f3cb',
        'onvocado-primary-dark': '#9eb74a',
        'onvocado-secondary': '#E6A23C',
        'onvocado-secondary-light': '#f3d7b3',
        'onvocado-secondary-dark': '#b9822f',
        'onvocado-red': '#F56C6C',
        'onvocado-green': '#10b981',
        'onvocado-gray': '#7c7e80',
        'onvocado-gray-dark': '#484848',
        'onvocado-gray-darker': '#3b3b3b',
        'onvocado-gray-light': '#9ca3af',
        'onvocado-gray-lighter': '#d9d9d9',
      },
       borderColor: (theme) => ({
        DEFAULT: "#e7edd1",
      }),
      backgroundImage: {
        // 'wheel': "url('/src/images/content/wheel.png')",
      },
      fontFamily: {
        'ease-standard': 'Ease Standard, Inter Tight',
        'inter': 'Inter',
        // 'neue-haas': 'Neue Haas Grot',
      },
      fontSize: {
        // '4xl': ['2.5rem', '2.5rem'],
        'base-lg': '1.0625rem',
        // '8xl': ['6.25rem', '1'],
      },
      lineHeight: {
        '6': '25.6px',
        '10': '49px',
        '11': '60px',
        '12': '74px',
      },
      aspectRatio: {
        '0.85/1': '0.85 / 1',
        '2.35/1': '2.35 / 1',
        '4/3': '4 / 3',
        '3/2': '3 / 2',
        '7/8': '7 / 8',
        '16/9': '16 / 9',
        '16/10': '16 / 10',
      },
      boxShadow: {
        'custom': '0 2px 3px 0 rgba(60,64,67,.3), 0 6px 10px 4px rgba(60,64,67,.15)',
        'nav': '0 1px rgb(72, 72, 72, 0.20)',
      },
      animation: {
        'infinite-scroll': 'infinite-scroll 45s linear infinite',
      },
      keyframes: {
        'infinite-scroll': {
          from: {transform: 'translateX(0)'},
          to: {transform: 'translateX(-100%)'},
        }
      }
    },
    container: {
      center: true,
      padding: '1rem'
    },
  },
  variants: {
    opacity: ({after}) => after(['disabled'])
  },
  plugins: [
    // require('@tailwindcss/forms'),
  ],
};
