
h1, h2, h3, h4, h5, h6 {
  @apply text-balance;
}

p, span {
  @apply text-pretty;
}

h1 {
  @apply text-6xl md:text-7xl tracking-tighter font-ease-standard font-bold;
}

h2 {
  @apply text-3xl font-ease-standard font-bold tracking-tight;
}

h3 {
  @apply text-2xl font-ease-standard font-bold tracking-tight;
}

h4 {
  @apply text-xl leading-tight font-ease-standard font-bold tracking-tight;
}

h5,
h6 {
  @apply text-lg font-ease-standard font-bold tracking-tight;
}

.markdown {
  h1 {
    @apply mb-12;
  }

  h2 {
    @apply mb-8;
  }

  h3, h4, h5, h6 {
    @apply mb-4;
  }

  p {
    @apply mb-6 md:mb-8 leading-6 last-of-type:mb-0 text-balance tracking-tight;
    font-feature-settings: "salt" on, "onum" on, "kern" on, "liga" on, "clig" on, "calt" on;
  }

  a {
    @apply underline;
    word-wrap: break-word;
  }

  ol {
    @apply mb-4 pl-8 list-decimal;
  }

  ul {
    @apply my-4 list-disc;
    margin-left: 1.45rem;

    li {
      @apply my-1.5;
    }
  }

  table {
    width: 100%;
    table-layout: fixed;

    > thead {
      th {
        border-radius: 9999px;
      }
    }

    tbody {
      td:first-of-type {
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
      }

      td:last-of-type {
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
      }
    }
  }
}

.menu-effect a:not(.button) {
  @apply relative inline-block text-body-black dark:text-body-gray antialiased;
  text-shadow: none;

  &::before {
    @apply absolute text-onvocado-primary-dark dark:text-onvocado-primary-dark bg-body-gray dark:bg-body-black;
    top: 0;
    left: 0;
    overflow: hidden;
    max-width: 0;
    content: attr(data-hover);
    transition: max-width 0.5s;
    white-space: nowrap;
  }

  &:hover::before,
  &:focus::before {
    max-width: 100%;
  }
}


.menu-effect.dark a:not(.button) {
  @apply text-body-gray;

  &::before {
    @apply bg-body-black text-onvocado-primary-dark;
  }
}

// Submenu items
.menu-effect .bg-onvocado-white a:not(.button) {
  &::before {
    background-color: #FEFEFE;
  }
}
