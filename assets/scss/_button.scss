/* =====================================================================
	##BUTTONS STYLES
	==================================================================== */

.button {
  @apply px-6 py-2.5 bg-onvocado-primary text-body-black hover:bg-body-black font-medium rounded-full outline-none focus:outline-none ease-linear transition-all duration-150 text-center;

  &.dark-bg {
    @apply hover:bg-onvocado-primary-dark hover:text-body-black;
  }

  &.outline {
    @apply bg-transparent hover:bg-transparent text-onvocado-primary border border-onvocado-primary;
  }

  &.small {
    @apply px-3 py-2 text-sm;
  }

  &.big {
    @apply px-10 py-4 text-base font-bold;
  }

  &.secondary {
    @apply bg-body-black text-onvocado-white rounded-full hover:bg-onvocado-primary hover:text-body-black;

    &.outline {
      @apply text-body-black bg-transparent border-body-black hover:border-onvocado-primary;
    }
  }

  &.orange {
    @apply bg-onvocado-secondary hover:bg-onvocado-secondary-dark;

    &.outline {
      @apply bg-transparent hover:bg-transparent text-onvocado-secondary border border-onvocado-secondary;
    }
  }

  &.gray {
    @apply bg-onvocado-gray-lighter hover:bg-onvocado-gray;
  }

  &.white {
    @apply bg-onvocado-white hover:bg-onvocado-gray-lighter text-onvocado-black;

    &.outline {
      @apply bg-transparent hover:bg-transparent text-onvocado-white border border-onvocado-white;
    }
  }
}

button:active, .button:active {
	transform: scale(0.99);
}
