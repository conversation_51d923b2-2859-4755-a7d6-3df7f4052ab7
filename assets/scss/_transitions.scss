.fade-delayed-enter-active, .fade-delayed-leave-active {
  transition: opacity .5s;
}

.fade-delayed-enter, .fade-delayed-leave-to /* .fade-delayed-leave-active below version 2.1.8 */
{
  opacity: 0;
}

.fade-delayed-enter-active {
  transition-delay: .2s; /* Delay duration */
}

.fade-enter-active, .fade-leave-active {
  transition: opacity .5s;
}

.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */
{
  opacity: 0;
}

// slide up
.slide-up-enter-active {
  transition: all 500ms ease;
  transition-property: opacity, transform;
}

.slide-up-enter {
  opacity: 0;
  transform: translateY(20%);
}

// slide left
.slide-left-enter-active {
  transition: all 500ms ease-in;
  transition-property: opacity, transform;
}

.slide-left-enter {
  opacity: 0;
  transform: translateX(20%);
}

.page-enter-active,
.page-leave-active {
  transition: opacity 0.4s;
}

.page-enter,
.page-leave-to {
  opacity: 0;
}
