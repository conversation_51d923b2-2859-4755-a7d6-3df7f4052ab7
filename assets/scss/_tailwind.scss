@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .section-wrapper {
    @apply py-5 md:py-6 xl:py-10 first-of-type:pb-5 first-of-type:pt-0;

    //&.text {
    //  @apply py-5 md:pt-6 md:pb-4 xl:pt-16 xl:pb-10 lg:w-2/4 md:mx-auto;
    //}
    //
    //&.image {
    //  @apply lg:w-2/4 mx-auto;
    //}
  }
}

@layer base {
  body {
    @apply text-base text-body-black bg-body-gray transition-colors;
  }

  .bg-body-black {
    @apply text-onvocado-primary;
  }

  section {
    @apply flex flex-col justify-center;
  }

  input {
    @apply outline-onvocado-primary;
  }

  @screen lg {
    // applying font size for lg breakpoint
    body {
      @apply text-base-lg;
    }
  }

  .svg-light {
    path {
      @apply fill-body-gray #{!important};
    }
  }

  .svg-dark {
    path {
      @apply fill-body-black;
    }
  }
}
