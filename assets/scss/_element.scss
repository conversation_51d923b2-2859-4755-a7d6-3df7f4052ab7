/* theme color */
// $--color-primary: $color-primary;
// $--color-info: $color-text-secondary;

/* icon font path, required */
// $--font-path: 'element-ui-custom-theme/fonts/';

// This is extracted from 'element-ui-custom-theme/index.css' in order to fix the font path
@font-face {
  font-family: element-icons;
  font-style: normal;
  font-weight: 400;
  src: url('@/assets/scss/element-ui-custom-theme/fonts/element-icons.woff') format('woff'),
    url('@/assets/scss/element-ui-custom-theme/fonts/element-icons.ttf') format('truetype');
  font-display: 'auto';
}

@import 'element-ui-custom-theme/index';
