@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Inter+Tight:wght@700&display=swap');


@font-face {
  font-family: 'Ease Standard';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: local('Ease Standard'), local('Ease Standard'),
  url('/fonts/EaseStandard/EaseStandard-Bold.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
  url('/fonts/EaseStandard/EaseStandard-Bold.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}

$font-family-base: "Inter", Helvetica, Arial, sans-serif;

html {
  /*font-size: 62.5%; // Now 10px = 1rem! */
  font-smoothing: antialiased;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: $font-family-base;
  font-feature-settings: "ss06" on,"kern" on,"ss04" on;
  text-rendering: optimizeLegibility;
}
