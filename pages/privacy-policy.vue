<template>
  <PreviewModeWrapper :previewMode="previewMode">

    <!-- Menu spacer -->
    <MenuSpacer/>

    <section class="xl:max-w-5xl container">
      <div class="h-[25vh] mb-24 flex flex-col justify-center md:items-center">
        <h1 class="mb-2">{{ privacyPolicy.data?.attributes?.title }}</h1>
        <span v-if="privacyPolicy.data?.attributes?.updatedAt">Last updated: {{
            $formatDate(privacyPolicy.data?.attributes?.updatedAt)
          }}</span>
      </div>
      <div v-html="privacyPolicy.data?.attributes?.details" class="markdown mb-10"></div>
    </section>

    <section class="xl:max-w-5xl container" v-for="(zone, index) in privacyPolicy?.data?.attributes?.sections"
             :key="index">
      <PageContentSections class="mb-8"
                           :zone="zone"/>
    </section>

    <Footer/>

    <SEO :seo="privacyPolicy.data?.attributes?.SEO"></SEO>
  </PreviewModeWrapper>
</template>

<script>
import privacyPolicyQuery from "@/apollo/queries/pages/privacyPolicy.gql";
import lenisScrollMixin from '@/mixins/lenisScrollMixin';
import previewModeMixin from '@/mixins/previewModeMixin';
import seoMixin from "@/mixins/seoMixin";

export default {
  name: 'privacy',
  colorMode: 'light',
  mixins: [lenisScrollMixin, previewModeMixin, seoMixin],
  data() {
    return {
      privacyPolicy: null,
    };
  },
  async asyncData({app, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    const responsePolicy = await app.apolloProvider.defaultClient.query({
      query: privacyPolicyQuery,
      variables: {
        locale: app.i18n?.locale || 'en',
      },
    }).catch((err) => {
      console.log(JSON.stringify(err, null, 2));
    });

    const privacyPolicy = responsePolicy.data.privacyPolicy;

    return {
      privacyPolicy,
      previewMode,
    };
  },
  methods: {
    getPageData() {
      return this.privacyPolicy?.data;
    },
  },
}
</script>
