<template>
  <PreviewModeWrapper :previewMode="previewMode">

    <!-- Menu spacer -->
    <MenuSpacer/>

    <section class="xl:max-w-5xl container">
      <div class="my-12 md:my-0 md:min-h-[50vh] flex flex-col justify-center md:items-center">
        <h1 class="mb-8 md:text-center">{{ product.data?.attributes?.title }}</h1>
        <div v-if="product.data?.attributes?.details" v-html="product.data?.attributes?.details"
             class="mb-8 md:mx-32 md:text-center text-xl"></div>
        <a href="https://app.onvocado.com/" class="button">{{ $t('ctaSection.buttonText') }} - <span class="font-normal opacity-80">{{ $t('ctaSection.freeText') }}</span></a>
      </div>
    </section>

    <section class="mb-12 md:mb-24" v-for="(zone, index) in product?.data?.attributes?.sections" :key="index">
      <PageContentSections class="mb-8"
                           :zone="zone"/>
    </section>

    <Footer/>

    <SEO :seo="product.data?.attributes?.SEO"
         :updated-at="product.data?.attributes?.updatedAt"></SEO>
  </PreviewModeWrapper>
</template>

<script>
import productQuery from "@/apollo/queries/pages/product.gql";
import lenisScrollMixin from '@/mixins/lenisScrollMixin';
import previewModeMixin from '@/mixins/previewModeMixin';
import seoMixin from '@/mixins/seoMixin';

export default {
  name: 'product',
  colorMode: 'light',
  mixins: [lenisScrollMixin, previewModeMixin, seoMixin],
  data() {
    return {
      product: null,
    };
  },
  async asyncData({app, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    const responseProduct = await app.apolloProvider.defaultClient.query({
      query: productQuery,
      variables: {
        locale: app.i18n?.locale || 'en',
      },
    }).catch((err) => {
      console.log(JSON.stringify(err, null, 2));
    });

    const product = responseProduct.data?.product;

    return {
      product,
      previewMode,
    };
  },
  methods: {
    getPageData() {
      return this.product?.data;
    },
  },
}
</script>
