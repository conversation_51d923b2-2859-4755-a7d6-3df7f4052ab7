<template>
  <PreviewModeWrapper :previewMode="previewMode">
    <MenuSpacer/>

    <section
      class="min-h-[30vh] lg:min-h-[35vh] mb-16 flex flex-col justify-around items-start md:items-center bg-body-black text-body-gray">
      <div class="px-4 mb-8">
        <h1 class="mb-4 capitalize text-center">{{ categoryTitle }}</h1>
        <div v-html="categoryDescription" class="markdown text-xl md:text-2xl"></div>
      </div>

      <!-- Topics List -->
      <div class="w-full overflow-x-auto">
        <ul class="w-fit md:w-auto mb-4 flex flex-row gap-4 flex-nowrap justify-center items-center">
          <li class="ml-4 px-2.5 py-0.5 text-body-white rounded-full text-lg cursor-pointer border">
            <a :href="$localePathWithTrailingSlash('/support/')">{{ $t('All') }}</a>
          </li>
          <li
            v-for="category in allCategories"
            :key="category.slug"
            :class="[
              'px-2.5 py-0.5 last:mr-4 text-body-white rounded-full text-lg whitespace-nowrap cursor-pointer',
              isCurrentCategory(category.slug)
                ? 'text-body-black bg-onvocado-primary'
                : 'border'
            ]">
            <nuxt-link
              :to="`${$i18n.locale === $i18n.defaultLocale ? '' : '/' + $i18n.locale}${i18nPages['support/topics/_slug'][$i18n.locale].replace(':slug', category.slug)}`"
              :class="{ 'active has-children': isCurrentCategory(category.slug) }">
              {{ category.title }}
            </nuxt-link>
          </li>
        </ul>
      </div>
    </section>

    <!-- Overview of support posts for the current topic -->
    <SectionSupportOverview class="container 2xl:max-w-7xl" :type="'support'" :support-items="supportPosts.data"/>

    <Footer/>

    <SEO :seo="categorySEO"/>
  </PreviewModeWrapper>
</template>

<script>
import categoryQuery from '@/apollo/queries/support/category.gql';
import postsByCategoryQuery from '@/apollo/queries/support/postsByCategory.gql';
import allCategoriesQuery from '@/apollo/queries/support/allCategories.gql';
import previewModeMixin from '@/mixins/previewModeMixin';
import seoMixin from '@/mixins/seoMixin';
import { i18nPages } from "@/i18n.config.js";
import { retryApolloQuery } from "~/utils/apolloRetry";

export default {
  name: 'SupportTopicsSlug',
  layout: 'support',
  colorMode: 'dark',
  mixins: [previewModeMixin, seoMixin],
  async asyncData({app, route}) {
    const categorySlug = route.params.slug;
    const locale = app.i18n?.locale || 'en';

    try {
      // Fetch current support topic details
      const {data: categoryData} = await app.apolloProvider.defaultClient.query({
        query: categoryQuery,
        variables: {slug: categorySlug, locale},
      });

      const category = categoryData?.supportCategories?.data[0];

      if (!category) {
        // Handle topic not found by returning nulls
        return {category: null, supportPosts: null, allCategories: []};
      }

      // Set route parameters for localization based on localizations of the support topic
      app.store.dispatch(
        "i18n/setRouteParams",
        Object.fromEntries(
          category.attributes.localizations.data.map(el => [
            el.attributes.locale,
            {slug: el.attributes.slug},
          ])
        )
      );

      // Fetch posts for the current support topic
      const {data: postsData} = await app.apolloProvider.defaultClient.query({
        query: postsByCategoryQuery,
        variables: {slug: categorySlug, locale},
      });

      // Fetch all support topics for the topics list
      const {data: categoriesData} = await app.apolloProvider.defaultClient.query({
        query: allCategoriesQuery,
        variables: {locale},
      });

      const allCategories = (categoriesData?.supportCategories?.data || [])
        .map(cat => ({
          slug: cat.attributes.slug,
          title: cat.attributes.title,
        }))
        .sort((a, b) => a.title.localeCompare(b.title));

      return {category, supportPosts: postsData?.supportPosts, allCategories};
    } catch (error) {
      console.error('Error fetching support category data:', error);
      return {category: null, supportPosts: null, allCategories: []};
    }
  },
  data() {
    return {
      supportPosts: null,
      category: null,
      allCategories: [],
      i18nPages,
    };
  },
  computed: {
    categoryTitle() {
      return this.category?.attributes?.title || '';
    },
    categoryDescription() {
      return this.category?.attributes?.description || '';
    },
    categorySEO() {
      return this.category?.attributes?.SEO || null;
    },
  },
  methods: {
    /**
     * Checks if the given slug matches the current support topic.
     * If slug is null, the "All" link is considered active.
     */
    isCurrentCategory(slug) {
      if (slug === null) {
        return this.$route.path === '/support' ||
          this.$route.path === '/bg/support' ||
          this.$route.path === '/es/support';
      }
      return this.category?.attributes?.slug === slug;
    },
  },
};
</script>
