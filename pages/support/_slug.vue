<template>
  <PreviewModeWrapper :previewMode="previewMode" style="font-size:0.975rem">
    <!-- Menu spacer -->
    <MenuSpacer/>

    <template v-if="supportPost">
      <div class="mt-8 mb-4 mx-4 md:mx-8 md:pb-8 flex justify-between items-center">
        <NuxtLink :to="$localePathWithTrailingSlash('/support/')">
          <div class="flex items-center gap-2 text-body-black font-bold text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#0f2815">
              <path d="m313-440 224 224-57 56-320-320 320-320 57 56-224 224h487v80H313Z"/>
            </svg>
            {{ $t('Back to Help Center') }}
          </div>
        </NuxtLink>
      </div>

      <!-- Page Header -->
      <section class="pt-12 md:pt-24 flex flex-col-reverse">
        <div class="mb-12 md:w-2/3 container xl:max-w-4xl">
          <h1 class="mb-3 md:mb-6 text-4xl md:text-5xl font-extrabold">
            {{ supportPost.attributes?.title }}
          </h1>
          <p class="mb-2 text-onvocado-gray text-lg">
            {{ supportPost.attributes?.subtitle }}
          </p>
          <ul class="flex gap-x-1.5">
            <li v-if="supportPost.attributes?.author?.data?.attributes">
              <span class="text-sm text-onvocado-gray">
                {{ $t('Written by') }}
                <b class="text-onvocado-gray-dark font-normal">
                  {{ supportPost.attributes?.author?.data?.attributes?.name }}
                </b> |
              </span>
            </li>
            <li>
              <span class="text-sm text-onvocado-gray-darker">
                {{ $formatDate(supportPost.attributes?.updatedAt) }}
              </span>
            </li>
          </ul>
        </div>
      </section>

      <!-- Flex Container for Aside (Table of Contents) and Content -->
      <div class="container xl:max-w-6xl flex flex-col lg:flex-row gap-8">
        <!-- Aside: Table of Contents -->
        <aside class="lg:w-1/4 mb-12">
          <div class="p-4 sticky top-20 bg-body-gray-dark rounded-lg z-10">
            <h4 class="mb-2">{{ $t('On this page') }}</h4>
            <hr class="mb-3"/>
            <ul>
              <template v-for="(item, index) in tocSections">
                <li class="py-1.5 leading-[1.1]" :key="index">
                  <a
                    :href="`#${item.id}`"
                    @click.prevent="scrollTo(item.id)"
                    :class="['toc-link leading-tight text-[16px]', { 'is-active': activeSection === item.id }]"
                    :aria-current="activeSection === item.id ? 'location' : undefined">
                    {{ item.title }}
                  </a>
                </li>
              </template>
            </ul>
          </div>
        </aside>

        <!-- Content Sections -->
        <div class="lg:w-3/4">
          <section
            class="mb-8 xl:max-w-4xl"
            v-for="(zone, index) in supportPost.attributes?.sections"
            :key="index"
            :id="getSectionId(zone, index)">
            <PageContentSections class="mb-8" :zone="zone"/>
          </section>
        </div>
      </div>
    </template>

    <PageNotFound v-else/>

    <Footer/>

    <SEO :seo="supportPost?.attributes?.SEO"
         :created-at="supportPost?.attributes?.createdAt"
         :updated-at="supportPost?.attributes?.updatedAt"/>
  </PreviewModeWrapper>
</template>

<script>
import supportPostQuery from "~/apollo/queries/support/post";
import lenisScrollMixin from "@/mixins/lenisScrollMixin";
import previewModeMixin from "@/mixins/previewModeMixin";
import fetchPostMixin from "@/mixins/fetchPostMixin";
import seoMixin from "@/mixins/seoMixin";
import { throttle } from "throttle-debounce";
import { retryApolloQuery } from "~/utils/retryApolloQuery";

export default {
  name: 'SupportPostSlug',
  layout: 'support',
  colorMode: 'light',
  mixins: [lenisScrollMixin, previewModeMixin, fetchPostMixin, seoMixin],
  async asyncData({ app, route, store }) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    try {
      // Fetch supportPost data with retry mechanism
      const supportPostResponse = await retryApolloQuery(
        app.apolloProvider.defaultClient,
        {
          query: supportPostQuery,
          variables: {
            slug: route.params.slug,
            locale: app.i18n?.locale || 'en',
          },
        },
        {
          maxAttempts: 5,
          onRetry: (error, attempt, delay) => {
            console.log(`Support post query retry ${attempt}/5 for slug: ${route.params.slug}`)
          },
          fallbackData: { data: { supportPosts: { data: [] } } }
        }
      );

      // Check if we have valid data
      if (!supportPostResponse?.data?.supportPosts?.data?.length) {
        console.warn(`No support post found for slug: ${route.params.slug}`);
        return { supportPost: null, previewMode };
      }

      const supportPost = supportPostResponse.data.supportPosts.data[0];

      // Set route parameters for localization
      app.store.dispatch(
        "i18n/setRouteParams",
        Object.fromEntries(
          supportPost.attributes.localizations.data.map((el) => [
            el.attributes.locale,
            { slug: el.attributes.slug },
          ])
        )
      );

      return {
        supportPost,
        previewMode,
      };
    } catch (error) {
      console.error('Error in support post asyncData:', error);
      return { supportPost: null, previewMode };
    }
  },
  data() {
    return {
      supportPost: null,
      activeSection: '',
    };
  },
  computed: {
    tocSections() {
      if (!this.supportPost) return [];
      const sections = this.supportPost.attributes?.sections || [];
      // Check if at least one section has a non-empty title.
      const hasTitledSection = sections.some(
        (section) => section.title && section.title.trim() !== ''
      );
      if (hasTitledSection) {
        // Map over sections with titles and include the generated id.
        return sections
          .map((section, index) => {
            if (section.title && section.title.trim() !== '') {
              return { title: section.title, id: this.generateId(section.title, index) };
            }
            return null;
          })
          .filter(item => item !== null);
      } else {
        // None of the sections have a title: return a single TOC item for the main title.
        return [{ title: this.supportPost.attributes.title, id: this.generateId(this.supportPost.attributes.title, 0) }];
      }
    },
  },
  mounted() {
    // Ensure lenis is initialized via your mixin.
    this.$nextTick(() => {
      this.setupLenisActiveSection();
    });
  },
  beforeRouteLeave(to, from, next) {
    this.$colorMode.preference = 'light';
    next();
  },
  methods: {
    // Generate a unique ID from the section title and index.
    generateId(title, index) {
      return (
        title
          .toLowerCase()
          .trim()
          .replace(/[^a-z0-9\s]/g, '')
          .replace(/\s+/g, '-') +
        `-${index}`
      );
    },
    // Decide which id to assign to each content section.
    // If none of the sections have a title, we assign an id only to the first section.
    getSectionId(zone, index) {
      const sections = this.supportPost.attributes?.sections || [];
      const hasTitledSection = sections.some(
        (section) => section.title && section.title.trim() !== ''
      );
      if (hasTitledSection) {
        return zone.title ? this.generateId(zone.title, index) : '';
      } else {
        // If none have titles, only assign an id to the first section.
        return index === 0 ? this.generateId(this.supportPost.attributes.title, 0) : '';
      }
    },
    // Scroll to the section with a smooth effect.
    scrollTo(id) {
      const element = document.getElementById(id);
      if (element) {
        const offset = 100; // Adjust offset if needed.
        const bodyRect = document.body.getBoundingClientRect().top;
        const elementRect = element.getBoundingClientRect().top;
        const elementPosition = elementRect - bodyRect;
        const offsetPosition = elementPosition - offset;

        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth',
        });
      }
    },
    // Setup lenis scroll events to update the active section.
    setupLenisActiveSection() {
      // Query all sections that have an id.
      const sections = Array.from(document.querySelectorAll('section[id]'));
      if (!sections.length) return;

      const updateActiveSection = () => {
        const viewportCenter = window.innerHeight / 2;
        let closestSectionId = '';
        let minDistance = Infinity;

        sections.forEach((section) => {
          const rect = section.getBoundingClientRect();
          const sectionCenter = rect.top + rect.height / 2;
          const distance = Math.abs(sectionCenter - viewportCenter);
          if (distance < minDistance) {
            minDistance = distance;
            closestSectionId = section.id;
          }
        });

        if (closestSectionId) {
          this.activeSection = closestSectionId;
        }
      };

      const throttledUpdate = throttle(50, updateActiveSection);

      if (this.lenisScroll && typeof this.lenisScroll.on === 'function') {
        this.lenisScroll.on('scroll', throttledUpdate);
      } else {
        window.addEventListener('scroll', throttledUpdate);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.breadcrumbs {
  @apply text-sm;
}

.toc-link {
  transition: color 0.3s ease, text-shadow 0.3s ease;

  &:hover,
  &.is-active {
    text-shadow: rgb(0, 0, 0) 1px 0px 0px;
  }
}

/* Adjust the sticky ToC on smaller screens */
@media (max-width: 1024px) {
  aside div {
    position: static !important;
  }
}
</style>
