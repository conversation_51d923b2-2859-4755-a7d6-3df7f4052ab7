<template>
  <PreviewModeWrapper :previewMode="previewMode">

    <!-- Menu spacer -->
    <MenuSpacer/>

    <section
      class="min-h-[30vh] mb-16 px-4 flex items-start md:items-center justify-center bg-body-black text-body-gray">
      <h1 class="mb-4">{{ support.data?.attributes?.title }}</h1>

      <div v-html="support.data?.attributes?.details" class="markdown text-xl md:text-2xl"></div>

      <!-- Search input -->
      <div class="w-full max-w-md mt-6 mb-8">
        <input
          type="text"
          v-model="searchQuery"
          @input="filterPosts"
          placeholder="Start your search, find your answers"
          class="w-full p-2 rounded-md text-body-black bg-onvocado-white placeholder-text-body-black"/>
      </div>
    </section>

    <!-- No results found message -->
    <section v-if="filteredPosts.length === 0"
             class="min-h-64 md:min-h-80 2xl:max-w-7xl container flex justify-center items-center">
      <div class="text-center py-10">
        <span class="text-xl font-bold">We couldn't find any articles for your search.</span>
      </div>
    </section>

    <!-- Grouped sections -->
    <SectionSupportCategoryOverview
      v-if="generalCategories.length > 0"
      class="container 2xl:max-w-7xl"
      :title="'General'"
      :support-category-items="generalCategories"/>

    <SectionSupportOverview
      v-if="integrationPosts.length > 0"
      class="container 2xl:max-w-7xl"
      :type="'support'"
      :title="'Integration Guides'"
      :support-items="integrationPosts"/>

    <Footer/>

    <SEO
      :seo="support.data?.attributes?.SEO"
      :created-at="support.data?.attributes?.createdAt"
      :updated-at="support.data?.attributes?.updatedAt"/>
  </PreviewModeWrapper>
</template>

<script>
import supportPostsQuery from "~/apollo/queries/support/posts";
import supportQuery from "@/apollo/queries/pages/support.gql";
import lenisScrollMixin from '@/mixins/lenisScrollMixin';
import previewModeMixin from '@/mixins/previewModeMixin';
import seoMixin from "@/mixins/seoMixin";

export default {
  name: 'support',
  layout: 'support',
  colorMode: 'dark',
  mixins: [lenisScrollMixin, previewModeMixin, seoMixin],
  data() {
    return {
      support: null,
      supportPosts: null,
      searchQuery: '',
      filteredPosts: []
    };
  },
  async asyncData({app, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    const responseSupport = await app.apolloProvider.defaultClient.query({
      query: supportQuery,
      variables: {
        locale: app.i18n?.locale || 'en',
      },
    }).catch((err) => {
      console.log(JSON.stringify(err, null, 2));
    });

    const support = responseSupport.data.support;
    const responseSupportPosts = await app.apolloProvider.defaultClient.query({
      query: supportPostsQuery,
      variables: {
        locale: app.i18n?.locale || 'en',
        publicationState: previewMode ? "PREVIEW" : "LIVE"
      },
    }).catch((err) => {
      console.log(JSON.stringify(err, null, 2));
    });

    const supportPosts = responseSupportPosts.data.supportPosts;

    return {
      support,
      supportPosts
    };
  },
  computed: {
    // "General" posts are those that either have no category or whose category is not "Integration Guides"
    generalPosts() {
      return this.filteredPosts.filter(post => {
        const category = post.attributes.support_category?.data;
        return !category || category.attributes.title !== "Integration Guides";
      });
    },
    // "Integrations" posts are those whose support_category title is "Integration Guides"
    integrationPosts() {
      return this.filteredPosts.filter(post => {
        const category = post.attributes.support_category?.data;
        return category && category.attributes.title === "Integration Guides";
      });
    },
    // Group general posts by category (or "Uncategorized" if none)
    generalCategories() {
      const groups = {};
      this.generalPosts.forEach(post => {
        const category = post.attributes.support_category?.data;

        // Use the category title or fallback to "Uncategorized"
        const key = category ? category.attributes.title : 'Uncategorized';

        if (!groups[key]) {
          groups[key] = {
            title: key,
            count: 0,
            posts: [],
            slug: category ? category.attributes.slug : 'uncategorized',
            // Fallback to 999 if no priority is defined
            priority: category?.attributes?.priority ?? 999
          };
        }

        groups[key].count++;
        groups[key].posts.push(post);
      });

      // Return groups as an array sorted by priority (ascending)
      return Object.values(groups).sort((a, b) => a.priority - b.priority);
    }
  },
  watch: {
    searchQuery() {
      this.filterPosts();
    }
  },
  methods: {
    filterPosts() {
      if (this.searchQuery.trim() === '') {
        this.filteredPosts = this.supportPosts.data;
      } else {
        const query = this.searchQuery.trim().toLowerCase();
        this.filteredPosts = this.supportPosts.data.filter(post =>
          post.attributes.title.toLowerCase().includes(query) ||
          (post.attributes.subtitle && post.attributes.subtitle.toLowerCase().includes(query))
        );
      }
    },
    getPageData() {
      return this.support?.data;
    },
  },
  created() {
    // Initialize the filtered posts with all support posts
    this.filteredPosts = this.supportPosts.data;
  }
};
</script>
