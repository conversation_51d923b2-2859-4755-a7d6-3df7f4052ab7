<template>
  <PreviewModeWrapper :previewMode="previewMode">

    <!-- Menu spacer -->
    <MenuSpacer/>

    <section class="xl:max-w-5xl container">
      <div class="my-12 md:my-0 md:min-h-[50vh] flex flex-col justify-center md:items-center">
        <h1 class="mb-8 md:text-center">{{ roadmap.data?.attributes?.title }}</h1>
        <div v-if="roadmap.data?.attributes?.details" v-html="roadmap.data?.attributes?.details"
             class="mb-8 md:mx-32 md:text-center text-xl"></div>
        <a href="https://app.onvocado.com/" class="button">{{ $t('ctaSection.buttonText') }} - <span class="font-normal opacity-80">{{ $t('ctaSection.freeText') }}</span></a>
      </div>
    </section>

    <SectionRoadmapSlider :data="roadmap.data?.attributes?.slider"/>

    <section class="mb-12 md:mb-24" v-for="(zone, index) in roadmap?.data?.attributes?.sections" :key="index">
      <PageContentSections class="mb-8"
                           :zone="zone"/>
    </section>

    <Footer/>

    <SEO :seo="roadmap.data?.attributes?.SEO"
         :updated-at="roadmap.data?.attributes?.updatedAt"></SEO>
  </PreviewModeWrapper>
</template>

<script>
import roadmapQuery from "@/apollo/queries/pages/roadmap.gql";
import lenisScrollMixin from '@/mixins/lenisScrollMixin';
import previewModeMixin from '@/mixins/previewModeMixin';
import seoMixin from '@/mixins/seoMixin';

export default {
  name: 'roadmap',
  colorMode: 'light',
  mixins: [lenisScrollMixin, previewModeMixin, seoMixin],
  data() {
    return {
      roadmap: null,
    };
  },
  async asyncData({app, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    const responseRoadmap = await app.apolloProvider.defaultClient.query({
      query: roadmapQuery,
      variables: {
        locale: app.i18n?.locale || 'en',
      },
    }).catch((err) => {
      console.log(JSON.stringify(err, null, 2));
    });

    const roadmap = responseRoadmap.data?.roadmap;

    return {
      roadmap,
      previewMode,
    };
  },
  methods: {
    getPageData() {
      return this.roadmap?.data;
    },
  },
}
</script>
