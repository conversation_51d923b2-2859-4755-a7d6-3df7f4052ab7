<template>
  <PreviewModeWrapper :previewMode="previewMode">

    <!-- Menu spacer -->
    <MenuSpacer/>

    <section class="xl:max-w-5xl container">
      <div class="my-12 md:my-0 md:min-h-[40vh] flex flex-col justify-center md:items-center">
        <h1 class="mb-8 md:text-6xl">{{ pricing.data?.attributes?.title }}</h1>
        <div v-html="pricing.data?.attributes?.details" class="mb-8 md:mx-32 md:text-center text-lg"></div>
      </div>
    </section>

    <SectionPricingPlans class="container 2xl:max-w-7xl" :available-plans="plans"/>

    <section class="mb-24 container 2xl:max-w-7xl" v-for="(zone, index) in pricing?.data?.attributes?.sections" :key="index">
      <PageContentSections class="mb-8"
                           :zone="zone"/>
    </section>

    <Footer/>

    <SEO :seo="pricing.data?.attributes?.SEO"
         :updated-at="pricing.data?.attributes?.updatedAt"></SEO>
  </PreviewModeWrapper>
</template>

<script>
import pricingQuery from "@/apollo/queries/pages/pricing.gql";
import lenisScrollMixin from '@/mixins/lenisScrollMixin';
import previewModeMixin from '@/mixins/previewModeMixin';
import seoMixin from "@/mixins/seoMixin";

export default {
  name: 'pricing',
  colorMode: 'light',
  mixins: [lenisScrollMixin, previewModeMixin, seoMixin],
  data() {
    return {
      pricing: null,
      plans: null,
    };
  },
  async asyncData({app, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    const responsePricing = await app.apolloProvider.defaultClient.query({
      query: pricingQuery,
      variables: {
        locale: app.i18n?.locale  || 'en',
      },
    }).catch((err) => {
      console.log(JSON.stringify(err, null, 2));
    });

    const pricing = responsePricing.data?.pricing;

    try {
      // Make a request to the REST API
      const restApiResponse = await app.$axios.$get('/v1/api/payment-plans/');

      return {
        pricing,
        previewMode,
        plans: restApiResponse, // This is your REST API data
      };
    } catch (err) {
      console.error('REST API request error:', err);
    }
  },
  methods: {
    getPageData() {
      return this.plans?.data;
    },
  },
}
</script>
