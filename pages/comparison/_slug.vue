<template>
  <PreviewModeWrapper :previewMode="previewMode">
    <!-- Menu spacer -->
    <MenuSpacer/>

    <template v-if="comparisonPost">

      <section
        class="min-h-[30vh] mb-16 pt-8 pb-8 lg:pt-16 flex flex-col justify-around items-start md:items-center bg-body-black text-body-gray">
        <div class="container flex flex-col items-center">
<!--          <h1 class="mb-4 text-center">{{ comparisonPost.attributes?.title }}</h1>-->

          <div v-html="comparisonPost.attributes?.subtitle"
               class="markdown max-w-xl m-auto mb-8 xl:mb-16 text-xl"></div>

          <div>
            <a href="https://app.onvocado.com/register"
               class="button dark:hover:text-body-black dark:hover:bg-onvocado-primary-dark big inline-block">
              {{ $t('supportSection.tryFree') }}</a>
          </div>
        </div>
      </section>

      <section class="mb-8 last-of-type:mb-16" v-for="(zone, index) in comparisonPost.attributes?.sections"
               :key="index">
        <PageContentSections class="mb-8 lg:mb-12 last-of-type:mb-0"
                             :zone="zone"/>
      </section>
    </template>

    <PageNotFound v-else/>

    <Footer/>

    <SEO :seo="comparisonPost?.attributes?.SEO"
         :created-at="comparisonPost?.attributes?.createdAt"
         :updated-at="comparisonPost?.attributes?.updatedAt"/>

  </PreviewModeWrapper>
</template>

<script>
import comparisonPostQuery from "~/apollo/queries/comparison/post";
import lenisScrollMixin from "@/mixins/lenisScrollMixin";
import previewModeMixin from "@/mixins/previewModeMixin";
import fetchPostMixin from "@/mixins/fetchPostMixin";
import seoMixin from "@/mixins/seoMixin";
import { retryApolloQuery } from "~/utils/retryApolloQuery";

export default {
  name: 'ComparisonPostSlug',
  colorMode: 'dark',
  mixins: [lenisScrollMixin, previewModeMixin, fetchPostMixin, seoMixin],
  async asyncData({app, route, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    try {
      // Fetch comparisonPost data with retry mechanism
      const comparisonPostResponse = await retryApolloQuery(
        app.apolloProvider.defaultClient,
        {
          query: comparisonPostQuery,
          variables: {
            slug: route.params.slug,
            locale: app.i18n?.locale || 'en',
          },
        },
        {
          maxAttempts: 5,
          onRetry: (error, attempt, delay) => {
            console.log(`Comparison post query retry ${attempt}/5 for slug: ${route.params.slug}`)
          },
          fallbackData: { data: { comparisonPosts: { data: [] } } }
        }
      );

      // Check if we have valid data
      if (!comparisonPostResponse?.data?.comparisonPosts?.data?.length) {
        console.warn(`No comparison post found for slug: ${route.params.slug}`);
        return { comparisonPost: null, previewMode };
      }

      const comparisonPost = comparisonPostResponse.data.comparisonPosts.data[0];

      // Set route parameters for localization
      app.store.dispatch(
        "i18n/setRouteParams",
        Object.fromEntries(
          comparisonPost.attributes.localizations.data.map((el) => [
            el.attributes.locale,
            {slug: el.attributes.slug},
          ])
        )
      );

      return {
        comparisonPost,
        previewMode
      }
    } catch (error) {
      console.error('Error in comparison post asyncData:', error);
      return { comparisonPost: null, previewMode };
    }
  },
  data() {
    return {
      comparisonPost: null,
    };
  },
  created() {
    const preview = this.$route.query.preview;

    if (preview && !this.comparisonPost) {
      // Set color as it doesn't automatically set after redirect
      this.$colorMode.preference = 'dark';
      this.fetchPost('comparisonPosts', comparisonPostQuery).then(comparisonPostData => {
        this.comparisonPost = comparisonPostData;
        this.$nextTick(() => {
          this.previewMode = true;
        });
      });
    }
  },
  methods: {
    getPageData() {
      return this.comparisonPost;
    },
  },
  beforeRouteLeave(to, from, next) {
    // Reset color as it doesn't automatically set after redirect (preview)
    this.$colorMode.preference = 'light';
    next()
  },
};
</script>
