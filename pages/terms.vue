<template>
  <PreviewModeWrapper :previewMode="previewMode">

    <!-- <PERSON>u spacer -->
    <MenuSpacer/>

    <section class="xl:max-w-5xl container">
      <div class="h-[25vh] mb-24 flex flex-col justify-center md:items-center">
        <h1 class="mb-2">{{ terms.data?.attributes?.title }}</h1>
        <span v-if="terms.data?.attributes?.updatedAt">Last updated: {{
            $formatDate(terms.data?.attributes?.updatedAt)
          }}</span>
      </div>
      <div v-html="terms.data?.attributes?.details" class="markdown mb-10"></div>
    </section>

    <section class="xl:max-w-5xl container" v-for="(zone, index) in terms?.data?.attributes?.sections" :key="index">
      <PageContentSections class="mb-8"
                           :zone="zone"/>
    </section>

    <Footer/>

    <SEO :seo="terms.data?.attributes?.SEO"></SEO>
  </PreviewModeWrapper>
</template>

<script>
import termsQuery from "@/apollo/queries/pages/terms.gql";
import lenisScrollMixin from '@/mixins/lenisScrollMixin';
import previewModeMixin from '@/mixins/previewModeMixin';
import seoMixin from "@/mixins/seoMixin";

export default {
  name: 'terms',
  colorMode: 'light',
  mixins: [lenisScrollMixin, previewModeMixin, seoMixin],
  data() {
    return {
      terms: null,
    };
  },
  async asyncData({app, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    const responseTerms = await app.apolloProvider.defaultClient.query({
      query: termsQuery,
      variables: {
        locale: app.i18n?.locale  || 'en',
      },
    }).catch((err) => {
      console.log(JSON.stringify(err, null, 2));
    });

    const terms = responseTerms.data.terms;

    return {
      terms,
      previewMode,
    };
  },
  methods: {
    getPageData() {
      return this.terms?.data;
    },
  },
}
</script>
