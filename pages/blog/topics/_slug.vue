<template>
  <PreviewModeWrapper :previewMode="previewMode">
    <MenuSpacer/>

    <section
      class="min-h-[30vh] lg:min-h-[35vh] mb-16 flex flex-col justify-around items-start md:items-center bg-body-black text-body-gray">
      <div class="px-4 mb-8">
        <h1 class="mb-4 capitalize text-center">{{ categoryTitle }}</h1>

        <div v-html="categoryDescription" class="markdown text-xl md:text-2xl"></div>
      </div>

      <!-- Categories List -->
      <div class="w-full overflow-x-auto">
        <ul class="w-fit md:w-auto mb-4 flex flex-row gap-4 flex-nowrap justify-center items-center">
          <li class="ml-4 px-2.5 py-0.5 text-body-white rounded-full text-lg cursor-pointer border">
            <a :href="$localePathWithTrailingSlash('/blog/')">{{ $t('All') }}</a>
          </li>
          <li v-for="category in allCategories"
              :class="['px-2.5 py-0.5 last:mr-4 text-body-white rounded-full text-lg whitespace-nowrap cursor-pointer', isCurrentCategory(category.slug) ? 'text-body-black bg-onvocado-primary' : 'border']"
              :key="category.slug">
            <nuxt-link
              :to="`${$i18n.locale === $i18n.defaultLocale ? '' : '/' + $i18n.locale}${i18nPages['blog/topics/_slug'][$i18n.locale].replace(':slug', category.slug)}`"
              :class="{ 'active has-children': isCurrentCategory(category.slug) }">
              {{ category.title }}
            </nuxt-link>
          </li>
          <li class="px-2.5 py-0.5 last:mr-4 text-body-white rounded-full text-lg whitespace-nowrap">
            <nuxt-link :to="$localePathWithTrailingSlash('/search/')">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#f8f9e8" viewBox="0 -960 960 960">
                <path
                  d="M784-120 532-372q-30 24-69 38t-83 14q-109 0-184.5-75.5T120-580q0-109 75.5-184.5T380-840q109 0 184.5 75.5T640-580q0 44-14 83t-38 69l252 252-56 56ZM380-400q75 0 127.5-52.5T560-580q0-75-52.5-127.5T380-760q-75 0-127.5 52.5T200-580q0 75 52.5 127.5T380-400Z"/>
              </svg>
            </nuxt-link>
          </li>
        </ul>
      </div>
    </section>

    <SectionPostOverview class="container 2xl:max-w-7xl" :posts="blogPosts.data"/>

    <Footer/>

    <SEO :seo="categorySEO"></SEO>
  </PreviewModeWrapper>
</template>

<script>
import categoryQuery from '@/apollo/queries/blog/category.gql';
import postsByCategoryQuery from '@/apollo/queries/blog/postsByCategory.gql';
import allCategoriesQuery from '@/apollo/queries/blog/allCategories.gql';
import previewModeMixin from '@/mixins/previewModeMixin';
import seoMixin from '@/mixins/seoMixin';
import {i18nPages} from "@/i18n.config.js";
import { retryApolloQuery } from "~/utils/retryApolloQuery";

export default {
  name: 'BlogTopicsSlug',
  colorMode: 'dark',
  mixins: [previewModeMixin, seoMixin],
  async asyncData({app, route, error}) {
    const categorySlug = route.params.slug;
    const locale = app.i18n?.locale || 'en';

    try {
      // Fetch current category details with retry
      const categoryResponse = await retryApolloQuery(
        app.apolloProvider.defaultClient,
        {
          query: categoryQuery,
          variables: {
            slug: categorySlug,
            locale,
          },
        },
        {
          maxAttempts: 3,
          onRetry: (err, attempt) => {
            console.log(`Blog category query retry ${attempt}/3 for slug: ${categorySlug}`)
          },
          fallbackData: { data: { blogCategories: { data: [] } } }
        }
      );

      const category = categoryResponse.data?.blogCategories?.data[0];

      if (!category) {
        console.warn(`No blog category found for slug: ${categorySlug}`);
        return {
          category: null,
          blogPosts: { data: [] },
          allCategories: []
        };
      }

      // Set route parameters for localization
      app.store.dispatch(
        "i18n/setRouteParams",
        Object.fromEntries(
          category.attributes.localizations.data.map((el) => [
            el.attributes.locale,
            {slug: el.attributes.slug},
          ])
        )
      );

      // Fetch posts for the current category with retry
      const blogPostsResponse = await retryApolloQuery(
        app.apolloProvider.defaultClient,
        {
          query: postsByCategoryQuery,
          variables: {
            slug: categorySlug,
            locale,
          },
        },
        {
          maxAttempts: 3,
          onRetry: (err, attempt) => {
            console.log(`Blog posts by category query retry ${attempt}/3`)
          },
          fallbackData: { data: { blogPosts: { data: [] } } }
        }
      );

      // Fetch all categories for the categories list with retry
      const categoriesResponse = await retryApolloQuery(
        app.apolloProvider.defaultClient,
        {
          query: allCategoriesQuery,
          variables: {locale},
        },
        {
          maxAttempts: 3,
          onRetry: (err, attempt) => {
            console.log(`All categories query retry ${attempt}/3`)
          },
          fallbackData: { data: { blogCategories: { data: [] } } }
        }
      );

      const allCategories = categoriesResponse.data?.blogCategories?.data
        .map(cat => ({
          slug: cat.attributes.slug,
          title: cat.attributes.title,
        }))
        .sort((a, b) => a.title.localeCompare(b.title)) || [];

      return {
        category,
        blogPosts: blogPostsResponse.data?.blogPosts || { data: [] },
        allCategories
      };
    } catch (err) {
      console.error('Error fetching blog category data:', err);

      // Return empty data to prevent template errors
      return {
        category: null,
        blogPosts: { data: [] },
        allCategories: []
      };
    }
  },
  data() {
    return {
      blogPosts: { data: [] },
      category: null,
      allCategories: [],
      i18nPages,
    };
  },
  computed: {
    categoryTitle() {
      return this.category?.attributes?.title || '';
    },
    categoryDescription() {
      return this.category?.attributes?.description || '';
    },
    categorySEO() {
      return this.category?.attributes?.SEO || null;
    },
  },
  methods: {
    /**
     * Checks if the given slug is the current category.
     * @param {String|null} slug - The slug to check. If null, checks if "All" is active.
     * @returns {Boolean} - True if active, false otherwise.
     */
    isCurrentCategory(slug) {
      if (slug === null) {
        return this.$route.path === '/blog' || this.$route.path === '/bg/blog' || this.$route.path === '/es/blog';
      }

      return this.category?.attributes?.slug === slug;
    },
  },
};
</script>
