<template>
  <PreviewModeWrapper :previewMode="previewMode">

    <!-- Menu spacer -->
    <MenuSpacer/>

    <section
      class="min-h-[30vh] mb-16 pt-8 lg:pt-16 flex flex-col justify-around items-start md:items-center bg-body-black text-body-gray">
      <div class="px-4 mb-8">
        <h1 class="mb-4">{{ blog.data?.attributes?.title }}</h1>

        <div v-html="blog.data?.attributes?.details" class="markdown text-xl md:text-2xl"></div>
      </div>

      <div class="w-full overflow-x-auto">
        <ul class="w-fit md:w-auto mb-4 flex flex-row gap-4 flex-nowrap justify-center items-center">
          <li class="ml-4 px-2.5 py-0.5 rounded-full text-lg bg-onvocado-primary text-body-black">
            <a :href="$localePathWithTrailingSlash('/blog/')" :class="{ active: !isCategoryPage }">{{ $t('All') }}</a>

          </li>
          <li v-for="category in categories"
              class="px-2.5 py-0.5 last:mr-4 text-body-white rounded-full text-lg whitespace-nowrap border"
              :key="category.slug">
            <nuxt-link
              :to="`${$i18n.locale === $i18n.defaultLocale ? '' : '/' + $i18n.locale}${i18nPages['blog/topics/_slug'][$i18n.locale].replace(':slug', category.slug)}`"
              :class="{ active: currentCategorySlug === category.slug }">
              {{ category.title }}
            </nuxt-link>
          </li>

          <li class="px-2.5 py-0.5 last:mr-4 text-body-white rounded-full text-lg whitespace-nowrap">
            <nuxt-link :to="$localePathWithTrailingSlash('/search/')">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#f8f9e8" viewBox="0 -960 960 960">
                <path
                  d="M784-120 532-372q-30 24-69 38t-83 14q-109 0-184.5-75.5T120-580q0-109 75.5-184.5T380-840q109 0 184.5 75.5T640-580q0 44-14 83t-38 69l252 252-56 56ZM380-400q75 0 127.5-52.5T560-580q0-75-52.5-127.5T380-760q-75 0-127.5 52.5T200-580q0 75 52.5 127.5T380-400Z"/>
              </svg>
            </nuxt-link>
          </li>
        </ul>
      </div>
    </section>


    <SectionPostOverview class="container 2xl:max-w-7xl" :posts="blogPosts.data"/>

    <Footer/>

    <SEO :seo="blog.data?.attributes?.SEO"
         :created-at="blog.data?.attributes?.createdAt"
         :updated-at="blog.data?.attributes?.updatedAt"></SEO>
  </PreviewModeWrapper>
</template>

<script>
import blogPostsQuery from "~/apollo/queries/blog/posts.gql";
import blogQuery from "@/apollo/queries/pages/blog.gql";
import lenisScrollMixin from '@/mixins/lenisScrollMixin';
import previewModeMixin from '@/mixins/previewModeMixin';
import seoMixin from '@/mixins/seoMixin';
import {i18nPages} from "@/i18n.config.js";
import { retryApolloQuery } from "~/utils/retryApolloQuery";

export default {
  name: 'blog',
  colorMode: 'dark',
  mixins: [lenisScrollMixin, previewModeMixin, seoMixin],
  data() {
    return {
      blog: null,
      blogPosts: null,
      i18nPages,
    };
  },
  async asyncData({app, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    try {
      // Fetch blog page data with retry
      const responseBlog = await retryApolloQuery(
        app.apolloProvider.defaultClient,
        {
          query: blogQuery,
          variables: {
            locale: app.i18n?.locale || 'en',
          },
        },
        {
          maxAttempts: 5,
          onRetry: (error, attempt) => {
            console.log(`Blog page query retry ${attempt}/5`)
          },
          fallbackData: { data: { blog: { data: null } } }
        }
      );

      const blog = responseBlog.data.blog;

      // Fetch blog posts with retry
      const responseBlogPosts = await retryApolloQuery(
        app.apolloProvider.defaultClient,
        {
          query: blogPostsQuery,
          variables: {
            locale: app.i18n?.locale || 'en',
            publicationState: previewMode ? "PREVIEW" : "LIVE"
          },
        },
        {
          maxAttempts: 5,
          onRetry: (error, attempt) => {
            console.log(`Blog posts query retry ${attempt}/5`)
          },
          fallbackData: { data: { blogPosts: { data: [] } } }
        }
      );

      const blogPosts = responseBlogPosts.data.blogPosts;

      return {
        blog,
        blogPosts
      };
    } catch (error) {
      console.error('Error in blog index asyncData:', error);
      return { blog: null, blogPosts: { data: [] } };
    }
  },
  computed: {
    // Check if the current route is a category page
    isCategoryPage() {
      return this.$route.name === 'blog-topics-slug';
    },
    // Get the current category slug from the route params
    currentCategorySlug() {
      return this.$route.params.slug || null;
    },
    categories() {
      const categoriesSet = [];
      if (this.blogPosts?.data) {
        this.blogPosts.data.forEach((post) => {
          post.attributes.blog_categories.data.forEach((category) => {
            const slug = category.attributes.slug;
            const title = category.attributes.title;
            if (!categoriesSet.find((cat) => cat.slug === slug)) {
              categoriesSet.push({slug, title});
            }
          });
        });
      }
      return categoriesSet.sort((a, b) => a.title.localeCompare(b.title));
    },
  },
  methods: {
    getPageData() {
      return this.blog?.data;
    },
  },
}
</script>
