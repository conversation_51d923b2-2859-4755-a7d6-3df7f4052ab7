<template>
  <PreviewModeWrapper :previewMode="previewMode">
    <MenuSpacer/>

    <template v-if="blogPost">
      <!-- Header Section -->
      <section class="py-12 md:py-16 2xl:py-20 flex flex-col-reverse">
        <div v-if="blogPost.attributes?.thumbnail?.data" class="mb-4 md:mb-auto container xl:max-w-6xl">
          <BlurHashResponsivePicture class="aspect-16/9" :image="blogPost.attributes.thumbnail.data"/>
        </div>
        <div class="mb-12 md:mb-16 md:w-2/3 container xl:max-w-4xl">
          <div v-if="blogPost.attributes?.featured" class="my-4 flex text-onvocado-primary-dark">
            <span class="flex items-end text-sm font-semibold uppercase tracking-wide">
              {{ $t('blogPost.featured') }} <FireIcon class="w-6 ml-1"/>
            </span>
          </div>

          <h1 class="mb-4 md:mb-10 text-4xl md:text-5xl font-extrabold">
            {{ blogPost.attributes?.title }}
          </h1>
          <p class="mb-4 text-onvocado-gray text-lg">
            {{ blogPost.attributes?.subtitle }}
          </p>
          <ul class="mb-8 flex gap-x-1.5">
            <li v-if="blogPost.attributes?.author?.data?.attributes">
              <span class="text-sm text-onvocado-gray">
                {{ $t('Written by') }} <b class="text-onvocado-gray-dark font-normal">{{
                  blogPost.attributes?.author?.data?.attributes?.name
                }}</b> |
              </span>
            </li>
            <li>
              <span class="text-sm text-onvocado-gray-darker">
                {{ $formatDate(blogPost.attributes?.updatedAt) }}
              </span>
            </li>
          </ul>

          <ul class="mb-3 md:mb-6 flex gap-x-1.5 breadcrumbs">
            <li class="whitespace-nowrap">
              <NuxtLink :to="$localePathWithTrailingSlash('/')">
                <span class="text-onvocado-primary-dark whitespace-nowrap">onvocado</span> /
              </NuxtLink>
            </li>
            <li class="whitespace-nowrap">
              <NuxtLink :to="$localePathWithTrailingSlash('/blog/')">
                <span class="text-onvocado-primary-dark whitespace-nowrap lowercase">{{ $t('Blog') }}</span> /
              </NuxtLink>
            </li>
            <li class="text-ellipsis whitespace-nowrap overflow-hidden">
              {{ blogPost.attributes?.title }}
            </li>
          </ul>
        </div>
      </section>

      <!-- Flex Container for ToC and Content -->
      <div class="container xl:max-w-6xl flex flex-col lg:flex-row gap-8">
        <!-- Table of Contents -->
        <aside class="lg:w-1/4 mb-12">
          <div class="p-4 sticky top-20 bg-body-gray-dark rounded-lg z-10">
            <h4 class="mb-2">{{ $t('On this page') }}</h4>
            <hr class="mb-3"/>
            <ul>
              <template v-for="(section, index) in blogPost.attributes?.sections">
                <li
                  v-if="section.title"
                  :key="index"
                  class="py-1.5 leading-[1.1]">
                  <a
                    :href="`#${generateId(section.title, index)}`"
                    @click.prevent="scrollTo(generateId(section.title, index))"
                    :class="['toc-link leading-tight text-[16px]', { 'is-active': activeSection === generateId(section.title, index) }]"
                    :aria-current="activeSection === generateId(section.title, index) ? 'location' : undefined">
                    {{ section.title }}
                  </a>
                </li>
              </template>
            </ul>
          </div>
        </aside>

        <!-- Content Sections -->
        <div class="lg:w-3/4">
          <section
            class="mb-4 md:mb-8 xl:max-w-4xl"
            v-for="(zone, index) in blogPost.attributes?.sections"
            :key="index"
            :id="zone.title ? generateId(zone.title, index) : ''">
            <PageContentSections class="mb-4" :zone="zone"/>
          </section>

          <section class="mb-4 md:mb-8 p-6 xl:max-w-4xl">
            <h3 class="text-xl mb-4">{{ $t('blogPost.shareThisArticle') }}</h3>
            <div class="flex gap-6">
              <a :href="shareUrls.facebook"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Share on Facebook">
                <FacebookIcon class="w-6 h-6 social-share-icon facebook"/>
              </a>
              <a
                :href="shareUrls.linkedin"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Share on LinkedIn">
                <LinkedInIcon class="w-5 h-6 social-share-icon linkedin"/>
              </a>
              <a :href="shareUrls.x"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Share on X">
                <XIcon class="w-5 h-6 social-share-icon x"/>
              </a>
            </div>
          </section>

          <section v-if="blogPost.attributes?.author?.data?.attributes"
                   class="mb-12 mt-8 p-6 bg-body-gray-dark rounded-lg">
            <h3 class="text-xl mb-2">{{ $t('blogPost.aboutTheAuthor') }}</h3>
            <div class="flex gap-4">
              <img
                v-if="blogPost.attributes?.author?.data?.attributes?.image?.data?.attributes?.url"
                class="w-10 h-10 rounded-full object-cover"
                :src="blogPost.attributes?.author.data.attributes.image.data?.attributes.url"
                :alt="blogPost.attributes?.author.data.attributes.image.data?.attributes.alternativeText || 'Author Image'"/>
              <div>
                <h4 class="text-lg font-bold text-onvocado-primary-dark">
                  {{ blogPost.attributes?.author.data.attributes.name }}
                </h4>
                <div class="text-onvocado-gray-dark markdown"
                     v-html="blogPost.attributes?.author.data.attributes.description"></div>
              </div>
            </div>
          </section>

          <!-- Comments Section -->
          <section class="mb-12 mt-8 p-6 bg-body-gray-dark rounded-lg">
            <h3 class="text-xl mb-4">{{ $t('blogPost.discussion') }}</h3>

            <!-- Display Comments -->
            <div v-if="comments && comments.length" class="pl-2">
              <ul>
                <li v-for="comment in comments" :key="comment.id" class="mb-4">
                  <div class="pb-2 border-b">
                    <p class="font-bold">{{ comment.author.name }}</p>
                    <p class="text-sm text-onvocado-gray-darker">{{ $formatDate(comment.createdAt) }}</p>
                    <p class="mt-2">{{ comment.content }}</p>
                  </div>
                </li>
              </ul>
            </div>
            <div v-else class="mb-8">
              <p>{{ $t('blogPost.noCommentsYet') }}</p>
            </div>

            <!-- Comment Form -->
            <div v-if="!showCommentsForm" class="flex justify-center items-center">
              <button class="button small outline secondary" @click.prevent="showCommentsForm = true">
                {{ $t('blogPost.writeAComment') }}
              </button>
            </div>
            <div v-else class="mt-8">
              <h4 v-if="!formSubmitted" class="text-lg mb-4">{{ $t('blogPost.whatIsOnYourMind') }}</h4>
              <form v-if="!formSubmitted" @submit.prevent="submitComment" class="pl-2 grid grid-cols-1 gap-6">
                <div>
                  <label for="author" class="text-sm">{{ $t('blogPost.name') }}</label>
                  <input
                    id="author"
                    v-model="newComment.author.name"
                    type="text"
                    required
                    class="w-full px-2.5 py-2 mt-1 text-sm block rounded-lg bg-transparent border border-onvocado-gray-light shadow-sm focus:border-onvocado-primary focus:ring focus:ring-onvocado-primary focus:ring-opacity-50 outline outline-0 transition-all focus:outline-0"/>
                </div>
                <div>
                  <label for="content" class="text-sm">{{ $t('blogPost.comment') }}</label>
                  <textarea
                    id="content"
                    v-model="newComment.content"
                    required
                    class="w-full px-2.5 py-2 mt-1 text-sm block rounded-lg bg-transparent border border-onvocado-gray-light shadow-sm focus:border-onvocado-primary focus:ring focus:ring-onvocado-primary focus:ring-opacity-50 outline outline-0 transition-all focus:outline-0"
                    rows="4"></textarea>
                </div>
                <div>
                  <button
                    type="submit"
                    :disabled="formLoading"
                    class="button">
                    {{ formLoading ? $t('blogPost.submitting') : $t('blogPost.submitComment') }}
                  </button>
                </div>
              </form>

              <div v-else class="mb-8">
                <h4 class="mb-2">{{ $t('blogPost.thanksForComment') }}</h4>
                <p class="pl-2">{{ $t('blogPost.commentReview') }}</p>
              </div>
            </div>
          </section>

        </div>
      </div>
    </template>

    <SectionRelatedPosts v-if="relatedPosts.length > 1" :posts="relatedPosts" :currentPostID="blogPost.id"/>

    <Footer/>

    <SEO :seo="blogPost.attributes.SEO"
         :tags="blogPost.attributes?.blog_categories.data"
         :created-at="blogPost.attributes?.createdAt"
         :updated-at="blogPost.attributes?.updatedAt"/>
  </PreviewModeWrapper>
</template>

<script>
import blogPostQuery from "~/apollo/queries/blog/post";
import blogPostsQuery from "~/apollo/queries/blog/posts";
import commentsQuery from "~/apollo/queries/comments/findAllFlat";
import createCommentMutation from "~/apollo/mutations/createComment";
import lenisScrollMixin from "@/mixins/lenisScrollMixin";
import previewModeMixin from "@/mixins/previewModeMixin";
import fetchPostMixin from "@/mixins/fetchPostMixin";
import FireIcon from "~/static/icons/fire.svg?inline";
import FacebookIcon from "~/static/icons/facebook.svg?inline";
import LinkedInIcon from "~/static/icons/linkedin.svg?inline";
import XIcon from "~/static/icons/x.svg?inline";
import seoMixin from "@/mixins/seoMixin";
import { retryApolloQuery } from "~/utils/apolloRetry";

export default {
  name: 'BlogPostSlug',
  colorMode: 'light',
  components: {FireIcon, FacebookIcon, LinkedInIcon, XIcon},
  mixins: [lenisScrollMixin, previewModeMixin, fetchPostMixin, seoMixin],
  async asyncData({app, route, store, error}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    try {
      // Fetch blogPost data with retry mechanism
      const blogPostResponse = await retryApolloQuery(
        app.apolloProvider.defaultClient,
        {
          query: blogPostQuery,
          variables: {
            slug: route.params.slug,
            locale: app.i18n?.locale || 'en',
          },
        },
        {
          maxAttempts: 5,
          onRetry: (error, attempt, delay) => {
            console.log(`Blog post query retry ${attempt}/5 for slug: ${route.params.slug}`)
          }
        }
      );

      const blogPost = blogPostResponse.data.blogPosts.data[0];

      if (!blogPost) {
        return error({ statusCode: 404, message: 'Blog post not found' });
      }

      const blogPostId = blogPost.id;

      // Set route parameters for localization
      app.store.dispatch(
        "i18n/setRouteParams",
        Object.fromEntries(
          blogPost.attributes.localizations.data.map((el) => [
            el.attributes.locale,
            {slug: el.attributes.slug},
          ])
        )
      );

      // Fetch relatedPosts data with retry mechanism
      let relatedPostsResponse = await retryApolloQuery(
        app.apolloProvider.defaultClient,
        {
          query: blogPostsQuery,
          variables: {
            locale: app.i18n?.locale || 'en',
          },
        },
        {
          maxAttempts: 3,
          onRetry: (error, attempt) => {
            console.log(`Related posts query retry ${attempt}/3`)
          }
        }
      );

      const relatedPosts = relatedPostsResponse.data.blogPosts.data;

      // Fetch comments for the blog post with retry mechanism
      const commentsResponse = await retryApolloQuery(
        app.apolloProvider.defaultClient,
        {
          query: commentsQuery,
          variables: {
            relation: `api::blog-post.blog-post:${blogPostId}`,
          },
        },
        {
          maxAttempts: 3,
          onRetry: (error, attempt) => {
            console.log(`Comments query retry ${attempt}/3`)
          }
        }
      );

      const comments = commentsResponse.data.findAllFlat.data;

      return {
        blogPost,
        relatedPosts,
        previewMode,
        comments,
      }
    } catch (err) {
      console.error('Error fetching blog post data:', err);

      // Check if it's a network error or GraphQL error
      if (err.networkError) {
        console.error('Network error details:', err.networkError);
        return error({ statusCode: 503, message: 'Service temporarily unavailable' });
      }

      if (err.graphQLErrors && err.graphQLErrors.length > 0) {
        console.error('GraphQL errors:', err.graphQLErrors);
        return error({ statusCode: 400, message: 'Invalid request' });
      }

      // Generic error fallback
      return error({ statusCode: 500, message: 'Internal server error' });
    }
  },
  async fetchComments() {
    const blogPostId = this.blogPost.id;

    const commentsResponse = await this.$apollo.query({
      query: commentsQuery,
      variables: {
        relation: `api::blog-post.blog-post:${blogPostId}`,
      },
      fetchPolicy: 'network-only',
    });

    this.comments = commentsResponse.data.findAllFlat.data;
  },
  data() {
    return {
      blogPost: null,
      relatedPosts: null,
      activeSection: '',
      lenis: null,
      observer: null,
      comments: [],
      newComment: {
        author: {name: '', id: ''},
        content: '',
      },
      formLoading: false,
      formSubmitted: false,
      showCommentsForm: false,
    };
  },
  computed: {
    currentUrl() {
      if (process.server) {
        return process.env.BASE_URL + this.$route.fullPath + '/';
      } else {
        return window.location.origin + this.$route.fullPath;
      }
    },
    shareUrls() {
      const encodedUrl = encodeURIComponent(this.currentUrl);
      const encodedTitle = encodeURIComponent(this.blogPost.attributes?.title || '');
      const appId = '3934656460098189';

      return {
        facebook: `https://www.facebook.com/dialog/feed?app_id=${appId}&link=${encodedUrl}&caption=${encodedTitle}&redirect_uri=${encodedUrl}`,
        linkedin: `https://www.linkedin.com/shareArticle?mini=true&source=Onvocado&title=${encodedTitle}&url=${encodedUrl}`,
        x: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`,
      };
    },
  },
  created() {
    const preview = this.$route.query.preview;

    if (preview && !this.blogPost) {
      // Set color as it doesn't automatically set after redirect
      this.$colorMode.preference = 'dark';
      this.fetchPost('blogPosts', blogPostQuery).then(blogPostData => {
        this.blogPost = blogPostData;

        this.fetchRelatedPosts().then(relatedPostsData => {
          this.relatedPosts = relatedPostsData;
          this.$nextTick(() => {
            this.previewMode = true;
          });
        });
      });
    }
  },
  async mounted() {
    try {
      await this.$recaptcha.init()
    } catch (e) {
      console.log(e);
    }
    this.setupIntersectionObserver();
    if (process.client) {
      this.newComment.author.id = this.getNumericId();
    }
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  methods: {
    getNumericId() {
      const authorId = localStorage.getItem('authorId');
      if (authorId) {
        return authorId;
      }
      // Generate a random 12-digit number
      const randomId = Math.floor(100000000000 + Math.random() * 900000000000).toString();
      localStorage.setItem('authorId', randomId);
      return randomId;
    },
    async fetchRelatedPosts(tagNames) {
      const relatedPostsResponse = await this.$apolloProvider.defaultClient.query({
        query: blogPostsQuery,
        variables: {
          locale: this.$i18n.locale,
        },
      });

      return relatedPostsResponse.data.blogPosts.data;
    },
    async submitComment() {
      this.formLoading = true;

      try {
        // Get the reCAPTCHA token
        const recaptchaToken = await this.$recaptcha.execute('submit_comment');
        const blogPostId = this.blogPost.id;

        await this.$apollo.mutate({
          mutation: createCommentMutation,
          variables: {
            input: {
              // Use content field as pipeline to send token to backend
              content: JSON.stringify({
                text: this.newComment.content,
                token: recaptchaToken,
              }),
              relation: `api::blog-post.blog-post:${blogPostId}`,
              author: {
                id: this.newComment.author.id,
                name: this.newComment.author.name,
              },
            },
          },
        });

        // Clear the form
        this.newComment.author.name = '';
        this.newComment.content = '';

        this.formSubmitted = true

      } catch (error) {
        console.error('Error submitting comment:', error);
      } finally {
        this.formLoading = false;
      }
    },
    getPageData() {
      return this.blogPost;
    },
    getPageComments() {
      return this.comments;
    },
    generateId(title, index) {
      return title
          .toLowerCase()
          .trim()
          .replace(/[^a-z0-9\s]/g, '')
          .replace(/\s+/g, '-')
          // Remove leading numbers and any following hyphens or spaces
          .replace(/^\d+[-\s]+/, '')
        + `-${index}`;
    },
    setupIntersectionObserver() {
      const options = {
        root: null, // viewport
        rootMargin: '0px',
        threshold: 0.6 // 60% of the section is visible
      };

      this.observer = new IntersectionObserver(this.handleIntersect, options);

      // Observe each section
      this.$nextTick(() => {
        const sections = document.querySelectorAll('section[id]');
        sections.forEach(section => {
          this.observer.observe(section);
        });
      });
    },
    handleIntersect(entries) {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.activeSection = entry.target.id;
        }
      });
    },
    scrollTo(ID) {
      const target = "#" + ID
      this.$store.dispatch('triggerScrollTo', {target, offset: -100});
    }
  },
  beforeRouteLeave(to, from, next) {
    // Reset color as it doesn't automatically set after redirect (preview)
    this.$colorMode.preference = 'light';
    next();
  },
};
</script>

<style lang="scss" scoped>
.breadcrumbs {
  @apply text-sm;
}

.toc-link {
  transition: color 0.3s ease, text-shadow 0.3s ease;

  &:hover,
  &.is-active {
    text-shadow: rgb(0, 0, 0) 1px 0px 0px;
  }
}

/* Adjust the sticky ToC on smaller screens */
@media (max-width: 1024px) {
  aside div {
    position: static !important;
  }
}
</style>
