<template>
  <div>
    <!-- Menu spacer -->
    <MenuSpacer/>

    <section
      class="min-h-[30vh] mb-16 px-4 flex items-start md:items-center justify-center bg-body-black text-body-gray">
      <h1 class="mb-4 ">{{ search.data?.attributes?.title }}</h1>

      <div v-html="search.data?.attributes?.details" class="markdown text-xl md:text-2xl"></div>

      <div class="w-full max-w-md mt-6 md:mt-12 mb-8">
        <form class="flex gap-2" @submit.prevent="onSearch">
          <input
            v-model="searchQuery"
            type="text"
            class="w-full px-3 py-2.5 mt-1 text-sm block rounded-lg bg-transparent border border-body-gray shadow-sm focus:border-onvocado-primary focus:ring focus:ring-onvocado-primary focus:ring-opacity-50 outline outline-0 transition-all focus:outline-0 placeholder-body-gray-darker"
            :placeholder="$t('search_placeholder')"
          />

          <button class="button dark-bg" type="submit">{{ $t('search_button') }}</button>
        </form>

      </div>
    </section>

    <section class="min-h-[30vh] mb-16 container xl:max-w-5xl">

      <div v-if="results.length">
        <h2 class="text-3xl font-bold mb-8">{{ $t('search_results_for', {query: searchQuery}) }}</h2>

        <div class="container 2xl:max-w-7xl">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 md:gap-x-12 gap-y-8 md:gap-y-16">
            <article
              v-for="(result, index) in results"
              :key="`${result.id}${index}`">
              <nuxt-link class="h-full block p-6 border rounded-lg bg-body-gray-dark hover:shadow-lg transition-shadow duration-300"
                :to="localePath(result.url)">{{ result.title }}</nuxt-link>
            </article>
          </div>
        </div>
      </div>
      <div v-else-if="searchPerformed">
        <h2 class="mb-2">{{ $t('no_results_heading') }}</h2>
        <p>{{ $t('no_results_subtext', {query: searchQuery}) }}</p>
      </div>
    </section>

    <Footer/>
  </div>
</template>

<script>
import searchQuery from "@/apollo/queries/pages/search.gql";
import lenisScrollMixin from '@/mixins/lenisScrollMixin';
import previewModeMixin from '@/mixins/previewModeMixin';
import seoMixin from '@/mixins/seoMixin';
import IntegrationOverviewItem from "@/components/IntegrationOverviewItem.vue";
import SupportOverviewItem from "@/components/SupportOverviewItem.vue";

export default {
  name: 'search',
  components: {SupportOverviewItem, IntegrationOverviewItem},
  colorMode: 'dark',
  mixins: [lenisScrollMixin, previewModeMixin, seoMixin],
  data() {
    return {
      searchQuery: this.$route.query.q || '',
      searchIndex: [],
      results: [],
      searchPerformed: false,
    };
  },
  async asyncData({app, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    const responseSearch = await app.apolloProvider.defaultClient.query({
      query: searchQuery,
      variables: {
        locale: app.i18n?.locale || 'en',
      },
    }).catch((err) => {
      console.log(JSON.stringify(err, null, 2));
    });

    const search = responseSearch.data.search;

    return {
      search,
    };
  },
  async mounted() {
    // Fetch the search index
    const response = await fetch('/search-index.json');
    this.searchIndex = await response.json();

    // Update Fuse.js with the search index
    this.$fuse.setCollection(this.searchIndex);

    // Perform search if query parameter is present
    if (this.searchQuery) {
      this.performSearch();
    }
  },
  methods: {
    onSearch() {
      // Update the URL query parameter
      this.$router.push({path: this.$route.path, query: {q: this.searchQuery}});
      this.performSearch();
    },
    performSearch() {
      const results = this.$fuse.search(this.searchQuery);
      // Filter results by current locale
      const localeResults = results
        .map((result) => result.item)
        .filter((item) => item.locale === this.$i18n.locale);
      this.results = localeResults;
      this.searchPerformed = true;
    },
  },
  watch: {
    searchQuery() {
      console.log('searchQuery')
      this.searchPerformed = false;
    },
    '$route.query.q'(newQuery) {
      this.searchQuery = newQuery || '';
      if (this.searchQuery) {
        this.performSearch();
      } else {
        this.results = [];
        this.searchPerformed = false;
      }
    },
  },
};
</script>
