<template>
  <PreviewModeWrapper :previewMode="previewMode"  class="home-page">

    <!-- Menu spacer -->
    <MenuSpacer/>

    <SectionHero :heroData="home.data?.attributes?.Hero"/>

    <SectionEditorPreview/>

    <template v-for="(zone, index) in home?.data?.attributes?.sections">
      <section :class="zone.__typename === 'ComponentGeneralTitleSection' ? '' : 'mb-16 md:mb-32 last-of-type:mb-0'"
               :key="index">
        <PageContentSections :zone="zone"/>
      </section>
    </template>

    <Footer/>

    <SEO :seo="home.data?.attributes?.SEO"
         :updated-at="home.data?.attributes?.updatedAt"></SEO>
  </PreviewModeWrapper>
</template>

<script>
import homeQuery from "@/apollo/queries/pages/home.gql";
import lenisScrollMixin from '@/mixins/lenisScrollMixin';
import previewModeMixin from '@/mixins/previewModeMixin';
import seoMixin from '@/mixins/seoMixin';
import { retryApolloQuery } from "~/utils/retryApolloQuery";

export default {
  name: 'index',
  colorMode: 'light',
  mixins: [lenisScrollMixin, previewModeMixin, seoMixin],
  data() {
    return {
      home: null,
    };
  },
  async asyncData({app, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    try {
      const responseHome = await retryApolloQuery(
        app.apolloProvider.defaultClient,
        {
          query: homeQuery,
          variables: {
            locale: app.i18n?.locale || 'en',
          },
        },
        {
          maxAttempts: 5,
          onRetry: (error, attempt) => {
            console.log(`Home page query retry ${attempt}/5`)
          },
          fallbackData: { data: { home: { data: null } } }
        }
      );

      const home = responseHome.data?.home;

      return {
        home,
        previewMode,
      };
    } catch (error) {
      console.error('Error in home page asyncData:', error);
      return { home: null, previewMode };
    }
  },
  methods: {
    getPageData() {
      return this.home?.data;
    },
  },
}
</script>

<style lang="scss">
.home-page {
  .markdown:not(.text-xl) {
    p, span, li {
      @apply text-lg;
    }
  }
}
</style>
