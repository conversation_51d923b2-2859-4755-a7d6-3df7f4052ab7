<template>
  <PreviewModeWrapper :previewMode="previewMode">

    <!-- Menu spacer -->
    <MenuSpacer/>

    <section class="xl:max-w-5xl container">
      <div class="my-12 md:my-0 md:min-h-[40vh] flex flex-col justify-center md:items-center">
        <h1 class="mb-8">{{ contact.data?.attributes?.title }}</h1>
        <div v-html="contact.data?.attributes?.details" class="mb-8 md:mx-32 md:text-center text-lg"></div>
      </div>
    </section>

    <section class="max-w-lg pb-32 container">
      <template v-if="!formSubmitted">
        <form action="#" class="pt-2 mx-4 grid grid-cols-1 gap-6">
          <template v-for="zone in contact.data.attributes.form">
            <component :is="zone.__typename"
                       :zone="zone"
                       :value="formValues[zone.label].value"
                       :error="formValues[zone.label].error"
                       @input="fieldInput(zone, $event)"
                       :key="zone.id">
            </component>
          </template>

          <button
            v-if="contact.data.attributes.form.length > 0"
            @click.capture.prevent="submitForm"
            :disabled="formLoading"
            :class="{'black': formLoading}"
            class="button">
            {{ formLoading ? $t('blogPost.submitting') : $t('blogPost.submitComment') }}
          </button>

        </form>
      </template>
      <template v-else>
        <div class="pt-2 pb-32 mx-4 text-center">
          <h3>{{ $t('thank_you_message') }}</h3>
        </div>
      </template>
    </section>


    <Footer/>

    <SEO :seo="contact.data?.attributes?.SEO"
         :updated-at="contact.data?.attributes?.updatedAt"></SEO>
  </PreviewModeWrapper>
</template>

<script>
import contactQuery from "@/apollo/queries/pages/contact.gql";
import lenisScrollMixin from '@/mixins/lenisScrollMixin';
import previewModeMixin from '@/mixins/previewModeMixin';
import seoMixin from "@/mixins/seoMixin";

export default {
  name: 'pricing',
  mixins: [lenisScrollMixin, previewModeMixin, seoMixin],
  data() {
    return {
      contact: null,
      formValues: null,
      formLoading: false,
      formSubmitted: false,
    };
  },
  async asyncData({app, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    const responseContact = await app.apolloProvider.defaultClient.query({
      query: contactQuery,
      variables: {
        locale: app.i18n?.locale || 'en',
      },
    }).catch((err) => {
      console.log(JSON.stringify(err, null, 2));
    });

    const contact = responseContact.data?.contact;

    // Initialize formValues
    const formValues = {};
    contact.data.attributes.form.forEach(zone => {
      formValues[zone.label] = {
        value: '',
        required: zone.required,
        error: false
      };
    });

    return {
      contact,
      formValues,
      previewMode,
    };
  },
  async mounted() {
    try {
      await this.$recaptcha.init()
    } catch (e) {
      console.log(e);
    }
  },
  methods: {
    fieldInput(zone, input) {
      this.formValues[zone.label].value = input;
      if (this.formValues[zone.label].error) this.formValues[zone.label].error = false;
    },
    async submitForm() {
      this.formLoading = true;

      if (!this.validateForm()) {
        this.formLoading = false;
        return; // Stop the form submission
      }

      try {
        const token = await this.$recaptcha.execute('submit_contact_form')

        // Simplified object construction
        const simplifiedFormValues = {};
        for (const key in this.formValues) {
          simplifiedFormValues[key] = this.formValues[key].value;
        }

        // Submit form data with simplifiedFormValues
        await this.$axios.post(`${process.env.BACKEND_URL}/api/ezforms/submit`, {
          token,
          formName: "Contact form",
          formData: simplifiedFormValues
        });

        // Handle success
        this.formSubmitted = true;
      } catch (error) {
        // Handle error
        console.error('Error during form submission:', error);
      } finally {
        this.formLoading = false;
      }
    },
    validateForm() {
      let isValid = true;
      for (const key in this.formValues) {
        const field = this.formValues[key];
        if (field.required && !field.value) {
          field.error = true; // Set an error message
          isValid = false;
        } else {
          field.error = false; // Clear the error message
        }
      }
      return isValid;
    },
    getPageData() {
      return this.contact?.data;
    },
  }
}
</script>
