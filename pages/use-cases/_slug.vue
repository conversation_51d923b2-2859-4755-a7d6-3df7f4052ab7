<template>
  <PreviewModeWrapper :previewMode="previewMode">
    <!-- Menu spacer -->
    <MenuSpacer/>

    <template v-if="useCasePost">
      <section class="pt-12 md:pt-24 flex flex-col-reverse">
        <div class="mb-12 md:w-2/3 container xl:max-w-4xl">
          <h1 class="mb-3 md:mb-6 md:text-center tracking-tight">
            {{ useCasePost.attributes?.title }}
          </h1>
          <div class="markdown mb-4 text-onvocado-gray" v-html="useCasePost.attributes?.subtitle"></div>
        </div>
      </section>

      <section class="mb-8 last-of-type:mb-0" v-for="(zone, index) in useCasePost.attributes?.sections"
               :key="index">
        <PageContentSections class="mb-8 last-of-type:mb-0"
                             :zone="zone"/>
      </section>
    </template>

    <PageNotFound v-else/>

    <Footer/>

    <SEO :seo="useCasePost?.attributes?.SEO"
         :created-at="useCasePost?.attributes?.createdAt"
         :updated-at="useCasePost?.attributes?.updatedAt"></SEO>

  </PreviewModeWrapper>
</template>

<script>
import useCasePostQuery from "~/apollo/queries/useCase/post";
import lenisScrollMixin from "@/mixins/lenisScrollMixin";
import previewModeMixin from "@/mixins/previewModeMixin";
import fetchPostMixin from "@/mixins/fetchPostMixin";
import seoMixin from "@/mixins/seoMixin";
import { retryApolloQuery } from "~/utils/retryApolloQuery";

export default {
  name: 'UseCasePostSlug',
  colorMode: 'light',
  mixins: [lenisScrollMixin, previewModeMixin, fetchPostMixin, seoMixin],
  async asyncData({app, route, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    try {
      // Fetch useCasePost data with retry mechanism
      const useCasePostResponse = await retryApolloQuery(
        app.apolloProvider.defaultClient,
        {
          query: useCasePostQuery,
          variables: {
            slug: route.params.slug,
            locale: app.i18n?.locale || 'en',
          },
        },
        {
          maxAttempts: 5,
          onRetry: (error, attempt, delay) => {
            console.log(`Use case post query retry ${attempt}/5 for slug: ${route.params.slug}`)
          },
          fallbackData: { data: { useCasePosts: { data: [] } } }
        }
      );

      // Check if we have valid data
      if (!useCasePostResponse?.data?.useCasePosts?.data?.length) {
        console.warn(`No use case post found for slug: ${route.params.slug}`);
        return { useCasePost: null, previewMode };
      }

      const useCasePost = useCasePostResponse.data.useCasePosts.data[0];

      // Set route parameters for localization
      app.store.dispatch(
        "i18n/setRouteParams",
        Object.fromEntries(
          useCasePost.attributes.localizations.data.map((el) => [
            el.attributes.locale,
            {slug: el.attributes.slug},
          ])
        )
      );

      return {
        useCasePost,
        previewMode
      }
    } catch (error) {
      console.error('Error in use case post asyncData:', error);
      return { useCasePost: null, previewMode };
    }
  },
  data() {
    return {
      useCasePost: null,
    };
  },
  created() {
    const preview = this.$route.query.preview;

    if (preview && !this.useCasePost) {
      // Set color as it doesn't automatically set after redirect
      this.$colorMode.preference = 'dark';
      this.fetchPost('useCasePosts', useCasePostQuery).then(useCasePostData => {
        this.useCasePost = useCasePostData;
        this.$nextTick(() => {
          this.previewMode = true;
        });
      });
    }
  },
  methods: {
    getPageData() {
      return this.useCasePost;
    },
  },
  beforeRouteLeave(to, from, next) {
    // Reset color as it doesn't automatically set after redirect (preview)
    this.$colorMode.preference = 'light';
    next()
  },
};
</script>
