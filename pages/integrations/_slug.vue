<template>
  <PreviewModeWrapper :previewMode="previewMode">
    <!-- Menu spacer -->
    <MenuSpacer/>

    <template v-if="integrationPost">
      <section class="pt-12 md:pt-24 md:mb-24 flex flex-col-reverse">
        <div class="mb-12 xl:w-2/3 container">
          <h1 class="mb-3 md:mb-6 text-4xl md:text-5xl xl:text-8xl font-extrabold">
            {{ integrationPost.attributes?.title }}
          </h1>
          <div v-if="integrationPost.attributes?.subtitle" class="mb-2 text-onvocado-gray text-lg"
               v-html="integrationPost.attributes?.subtitle"></div>

          <ul class="mb-4 md:mb-8 flex gap-x-1.5">
            <li v-if="integrationPost.attributes?.author?.data?.attributes">
              <span class="text-sm text-onvocado-gray">{{ $t('Written by') }} <b
                class="text-onvocado-gray-dark font-normal">{{ integrationPost.attributes?.author?.data?.attributes?.name }}</b> |</span>
            </li>
            <li>
              <span class="text-sm text-onvocado-gray-darker">
                {{ $formatDate(integrationPost.attributes?.updatedAt) }}
              </span>
            </li>
          </ul>

          <div class="grid grid-cols-3 items-center gap-4">
            <nuxt-img class="aspect-16/9 rounded-xl" src="/images/logos/integrations/onvocado_integration.png"
                      alt="Onvocado"
                      width="1960" height="1060" lazy/>
            <span class="text-3xl lg:text-4xl 2xl:text-7xl text-center font-ease-standard">&</span>
            <BlurHashResponsivePicture class="aspect-16/9 rounded-xl overflow-hidden"
                                       :img-class="'!object-contain'"
                                       :image="integrationPost.attributes?.thumbnail?.data"/>
          </div>
        </div>
      </section>

      <section class="mb-8 last-of-type:mb-16" v-for="(zone, index) in integrationPost.attributes?.sections"
               :key="index">
        <PageContentSections class="mb-8 lg:mb-12 last-of-type:mb-0"
                             :zone="zone"/>
      </section>
    </template>

    <PageNotFound v-else/>

    <Footer/>

    <SEO :seo="integrationPost.attributes?.SEO"
         :created-at="integrationPost.attributes?.createdAt"
         :updated-at="integrationPost.attributes?.updatedAt"></SEO>

  </PreviewModeWrapper>
</template>

<script>
import integrationPostQuery from "~/apollo/queries/integrations/post";
import lenisScrollMixin from "@/mixins/lenisScrollMixin";
import previewModeMixin from "@/mixins/previewModeMixin";
import fetchPostMixin from "@/mixins/fetchPostMixin";
import FireIcon from "~/static/icons/fire.svg?inline";
import seoMixin from "@/mixins/seoMixin";
import { retryApolloQuery } from "~/utils/retryApolloQuery";

export default {
  name: 'IntegrationPostSlug',
  colorMode: 'light',
  components: {FireIcon},
  mixins: [lenisScrollMixin, previewModeMixin, fetchPostMixin, seoMixin],
  async asyncData({app, route, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    try {
      // Fetch integrationPost data with retry mechanism
      const integrationPostResponse = await retryApolloQuery(
        app.apolloProvider.defaultClient,
        {
          query: integrationPostQuery,
          variables: {
            slug: route.params.slug,
            locale: app.i18n?.locale || 'en',
          },
        },
        {
          maxAttempts: 5,
          onRetry: (error, attempt, delay) => {
            console.log(`Integration post query retry ${attempt}/5 for slug: ${route.params.slug}`)
          },
          fallbackData: { data: { integrationPosts: { data: [] } } }
        }
      );

      // Check if we have valid data
      if (!integrationPostResponse?.data?.integrationPosts?.data?.length) {
        console.warn(`No integration post found for slug: ${route.params.slug}`);
        return { integrationPost: null, previewMode };
      }

      const integrationPost = integrationPostResponse.data.integrationPosts.data[0];

      // Set route parameters for localization
      app.store.dispatch(
        "i18n/setRouteParams",
        Object.fromEntries(
          integrationPost.attributes.localizations.data.map((el) => [
            el.attributes.locale,
            {slug: el.attributes.slug},
          ])
        )
      );

      return {
        integrationPost,
        previewMode
      }
    } catch (error) {
      console.error('Error in integration post asyncData:', error);
      return { integrationPost: null, previewMode };
    }
  },
  data() {
    return {
      integrationPost: null,
    };
  },
  created() {
    const preview = this.$route.query.preview;

    if (preview && !this.integrationPost) {
      // Set color as it doesn't automatically set after redirect
      this.$colorMode.preference = 'dark';
      this.fetchPost('integrationPosts', integrationPostQuery).then(integrationPostData => {
        this.integrationPost = integrationPostData;
        // ToDo add related posts logic
        // this.fetchRelatedPosts(integrationPostData.attributes.tags.data.map(tag => tag.attributes.name)).then(relatedPostsData => {
        //   this.relatedPosts = relatedPostsData;
        //   this.$nextTick(() => {
        //     this.previewMode = true;
        //   });
        // });
        this.$nextTick(() => {
          this.previewMode = true;
        });
      });
    }
  },
  methods: {
    getPageData() {
      return this.integrationPost;
    },
  },
  beforeRouteLeave(to, from, next) {
    // Reset color as it doesn't automatically set after redirect (preview)
    this.$colorMode.preference = 'light';
    next()
  },
};
</script>
