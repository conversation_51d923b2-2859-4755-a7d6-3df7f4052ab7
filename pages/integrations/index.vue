<template>
  <PreviewModeWrapper :previewMode="previewMode">

    <!-- Menu spacer -->
    <MenuSpacer/>

    <section class="min-h-[30vh] mb-16 px-4 flex items-start md:items-center justify-center bg-body-black text-body-gray md:text-center">
      <h1 class="mb-4 ">{{ integrations.data?.attributes?.title }}</h1>

      <div v-html="integrations.data?.attributes?.details" class="markdown text-xl md:text-2xl mb-8"></div>
    </section>

    <SectionSupportOverview v-if="integrationsPosts" class="container 2xl:max-w-7xl" :type="'integrations'" :title="'Integrations Center'" :support-items="integrationsPosts.data"/>

    <Footer/>

    <SEO :seo="integrations.data?.attributes.SEO"
         :created-at="integrations.data?.attributes?.createdAt"
         :updated-at="integrations.data?.attributes?.updatedAt"></SEO>
  </PreviewModeWrapper>
</template>

<script>
import integrationsPostsQuery from "~/apollo/queries/integrations/posts";
import integrationsQuery from "@/apollo/queries/pages/integrations.gql";
import lenisScrollMixin from '@/mixins/lenisScrollMixin';
import previewModeMixin from '@/mixins/previewModeMixin';
import seoMixin from "@/mixins/seoMixin";

export default {
  name: 'integrations',
  colorMode: 'dark',
  mixins: [lenisScrollMixin, previewModeMixin, seoMixin],
  data() {
    return {
      integrations: null,
      integrationsPosts: null,
      searchQuery: '',
    };
  },
  async asyncData({app, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    const responseIntegrations = await app.apolloProvider.defaultClient.query({
      query: integrationsQuery,
      variables: {
        locale: app.i18n?.locale  || 'en',
      },
    }).catch((err) => {
      console.log(JSON.stringify(err, null, 2));
    })

    const integrations = responseIntegrations.data.integration;
    const responseIntegrationsPosts = await app.apolloProvider.defaultClient.query({
      query: integrationsPostsQuery,
      variables: {
        locale: app.i18n?.locale  || 'en',
        publicationState: previewMode ? "PREVIEW" : "LIVE"
      },
    }).catch((err) => {
      console.log(JSON.stringify(err, null, 2));
    })

    const integrationsPosts = responseIntegrationsPosts.data.integrationPosts;

    return {
      integrations,
      integrationsPosts
    }
  },
  methods: {
    getPageData() {
      return this.integrations?.data;
    },
  },
}
</script>
