// This file is only used to add correct routes when generating static site
import {GraphQLClient, gql} from 'graphql-request'

export default class BlogClient {
  constructor() {
    const strapiGraphQLPath = `${process.env.BACKEND_URL || 'http://localhost:1337'}/graphql`
    // const strapiGraphQLPath = 'https://cms.onvocado.com/graphql'; // local production test

    this.graphQLClient = new GraphQLClient(strapiGraphQLPath, {
      timeout: 60000, // 60 seconds timeout
      retry: {
        retries: 3,
        factor: 2,
        minTimeout: 1000,
        maxTimeout: 15000,
      },
      // credentials: 'include',
      // mode: 'cors',
      // method: 'GET',
      // jsonSerializer: {
      //   parse: JSON.parse,
      //   stringify: JSON.stringify,
      // },
      // headers: {
      //   authorization: 'Bearer MY_TOKEN',
      // },
    })
  }

  async request(query, variables) {
    const maxRetries = 3;
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.graphQLClient.request(query, variables);
      } catch (error) {
        lastError = error;
        console.error(`GraphQL request failed (attempt ${attempt}/${maxRetries}):`, error.message);

        if (attempt < maxRetries) {
          // Wait before retrying (exponential backoff)
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // If we're in static generation, return empty data instead of throwing
    if (process.static && process.client === false) {
      console.warn('All retry attempts failed. Returning empty data to continue static generation.');
      return {};
    }

    throw lastError;
  }

  async getAllBlogPosts(lang) {
    const allBlogPostsQuery =
    gql`query BlogPost($locale: I18NLocaleCode!, $publicationState: PublicationState = LIVE) {
      blogPosts(locale: $locale, publicationState:$publicationState, sort: "publishedDate:desc", pagination: { limit: -1 }) {
        data {
          id
          attributes {
            title
            slug
            publishedAt
            #            Sections for the search
            sections {
              __typename
              ... on ComponentPostTextSection {
                title
                content
              }
              ... on ComponentPostImageSection {
                image {
                  data {
                    id
                  }
                }
              }
              ... on ComponentPostImageTextSection {
                title
                content
                image {
                  data {
                    id
                  }
                }
              }
              ... on ComponentPostFaqSection {
                title
                description
                questions {
                  question
                  answer
                }
              }
            }
          }
        }
      }
    }`

    try {
      return await this.request(allBlogPostsQuery, {locale: lang});
    } catch (error) {
      console.error(`Error fetching blog posts for locale ${lang}:`, error);
      return { blogPosts: { data: [] } };
    }
  }

  getAllBlogCategories(lang) {
    const allBlogCategoriesQuery =
    gql`query AllCategories($locale: I18NLocaleCode!) {
      blogCategories(locale: $locale, sort: "title:asc") {
        data {
          attributes {
            slug
            title
          }
        }
      }
    }
    `

    return this.graphQLClient.request(allBlogCategoriesQuery, {locale: lang})
  }

  getAllBlogPostsRSS(lang) {
    const allBlogPostsQuery =
    gql`query BlogPost($locale: I18NLocaleCode!) {
      blogPosts(locale: $locale) {
        data {
          id
          attributes {
            title
            slug
            publishedDate
            createdAt
            sections {
              __typename
              ... on ComponentPostTextSection {
                title
                content
              }
              ... on ComponentPostImageSection {
                image {
                  data {
                    attributes {
                      formats
                      blurhash
                      alternativeText
                    }
                  }
                }
              }
              ... on ComponentPostImageTextSection {
                title
                content
                imageFirst
                isColumn
                image {
                  data {
                    attributes {
                      formats
                      blurhash
                      alternativeText
                    }
                  }
                }
              }
              ... on ComponentPostSliderSection {
                images {
                  data {
                    attributes {
                      formats
                      blurhash
                      alternativeText
                    }
                  }
                }
              }
            }
            SEO {
              metaTitle
              metaDescription
            }
          }
        }
      }
    }`

    return this.graphQLClient.request(allBlogPostsQuery, {locale: lang})
  }

  getAllSupportCategories(lang) {
    const allSupportCategoriesQuery =
    gql`query AllCategories($locale: I18NLocaleCode!) {
      supportCategories(locale: $locale, sort: "title:asc") {
        data {
          attributes {
            slug
            title
          }
        }
      }
    }
    `

    return this.graphQLClient.request(allSupportCategoriesQuery, {locale: lang})
  }

  getAllSupportPosts(lang) {
    const allSupportPostsQuery =
    gql`query SupportPost($locale: I18NLocaleCode!, $publicationState: PublicationState = LIVE) {
      supportPosts(locale: $locale, publicationState:$publicationState, sort: "publishedDate:desc", pagination: { limit: -1 }) {
        data {
          id
          attributes {
            title
            slug
          }
        }
      }
    }`

    return this.graphQLClient.request(allSupportPostsQuery, {locale: lang})
  }

  getAllUseCasePosts(lang) {
    const allUseCasePostsQuery =
    gql`query UseCasePost($locale: I18NLocaleCode!, $publicationState: PublicationState = LIVE) {
      useCasePosts(locale: $locale, publicationState:$publicationState, pagination: { limit: -1 }) {
        data {
          id
          attributes {
            title
            slug
          }
        }
      }
    }`

    return this.graphQLClient.request(allUseCasePostsQuery, {locale: lang})
  }

  getAllIntegrationPosts(lang) {
    const allIntegrationPostsQuery =
    gql`query Integration($locale: I18NLocaleCode!, $publicationState: PublicationState = LIVE) {
      integrationPosts(locale: $locale, publicationState:$publicationState, sort: "publishedDate:desc", pagination: { limit: -1 }) {
        data {
          id
          attributes {
            title
            slug
          }
        }
      }
    }`

    return this.graphQLClient.request(allIntegrationPostsQuery, {locale: lang})
  }

  getAllTemplatePosts(lang) {
    const allTemplatePostsQuery =
    gql`query Template($locale: I18NLocaleCode!, $publicationState: PublicationState = LIVE) {
      templatePosts(locale: $locale, publicationState:$publicationState, sort: "publishedDate:desc", pagination: { limit: -1 }) {
        data {
          id
          attributes {
            title
            slug
          }
        }
      }
    }`

    return this.graphQLClient.request(allTemplatePostsQuery, {locale: lang})
  }

  getAllComparisonPosts(lang) {
    const allComparisonPostsQuery =
    gql`query Comparison($locale: I18NLocaleCode!, $publicationState: PublicationState = LIVE) {
      comparisonPosts(locale: $locale, publicationState:$publicationState, sort: "publishedDate:desc", pagination: { limit: -1 }) {
        data {
          id
          attributes {
            title
            slug
          }
        }
      }
    }`

    return this.graphQLClient.request(allComparisonPostsQuery, {locale: lang})
  }
}
