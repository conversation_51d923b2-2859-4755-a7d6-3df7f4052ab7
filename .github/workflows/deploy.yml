name: Production Deployment # a name for your workflow
on: # trigger on push event and main branch to the repo
  push:
    branches: [main]
  workflow_dispatch: # must be included in your .yml file for manually triggering event

permissions: read-all

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions: read-all
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_DEFAULT_REGION: us-east-1
      AWS_DEFAULT_OUTPUT: json
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Set execute permissions on script
        run: chmod +x ${{ github.workspace }}/.github/scripts/amplify-deploy.sh
      - name: Deploy
        run: ${{ github.workspace }}/.github/scripts/amplify-deploy.sh d33mjow95y9byj main
